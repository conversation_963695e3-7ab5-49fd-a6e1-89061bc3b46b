"""
Configuration module for Banking Data Pipeline
Centralizes all configuration settings for the Medallion Architecture
"""

from dataclasses import dataclass
from typing import Dict, List
from pyspark.sql.types import *

@dataclass
class PipelineConfig:
    """Central configuration for the banking data pipeline"""

    # Docker Environment Configuration
    DOCKER_BASE_PATH: str = "/home/<USER>/Volumes/dlt_banking_project_catalog/dlt_project_schema"

    # Catalog and Schema Configuration (for DLT compatibility)
    CATALOG_NAME: str = "dlt_banking_project_catalog"
    SCHEMA_NAME: str = "dlt_project_schema"

    # Docker Volume Paths
    RAW_VOLUME_PATH: str = f"{DOCKER_BASE_PATH}/raw"
    CHECKPOINT_PATH: str = f"{DOCKER_BASE_PATH}/checkpoints"
    BRONZE_PATH: str = f"{DOCKER_BASE_PATH}/bronze"
    SILVER_PATH: str = f"{DOCKER_BASE_PATH}/silver"
    GOLD_PATH: str = f"{DOCKER_BASE_PATH}/gold"

    # Data Source Paths (Year-wise structure)
    RAW_CUSTOMER_2023_PATH: str = f"{RAW_VOLUME_PATH}/2023/customers"
    RAW_CUSTOMER_2024_PATH: str = f"{RAW_VOLUME_PATH}/2024/customers"
    RAW_ACCOUNTS_2023_PATH: str = f"{RAW_VOLUME_PATH}/2023/accounts"
    RAW_ACCOUNTS_2024_PATH: str = f"{RAW_VOLUME_PATH}/2024/accounts"
    
    # Table Names
    LANDING_CUSTOMER_TABLE: str = "landing_customer_incremental"
    LANDING_TRANSACTIONS_TABLE: str = "landing_accounts_transactions_incremental"
    
    BRONZE_CUSTOMER_TABLE: str = "bronze_customer_cleaned"
    BRONZE_TRANSACTIONS_TABLE: str = "bronze_accounts_transactions_cleaned"
    
    SILVER_CUSTOMER_TABLE: str = "silver_customer_enriched"
    SILVER_TRANSACTIONS_TABLE: str = "silver_transactions_enriched"
    SILVER_ACCOUNTS_TABLE: str = "silver_accounts_summary"
    
    GOLD_CUSTOMER_ANALYTICS_TABLE: str = "gold_customer_analytics"
    GOLD_TRANSACTION_SUMMARY_TABLE: str = "gold_transaction_summary"
    GOLD_RISK_METRICS_TABLE: str = "gold_risk_metrics"
    GOLD_MONTHLY_KPI_TABLE: str = "gold_monthly_kpi"
    
    # Data Quality Thresholds
    MIN_PHONE_LENGTH: int = 10
    MAX_PHONE_LENGTH: int = 15
    MIN_TRANSACTION_AMOUNT: float = 0.01
    MAX_TRANSACTION_AMOUNT: float = 1000000.0
    
    # Business Rules
    VALID_ACCOUNT_TYPES: List[str] = ["Current", "Savings", "Credit", "Investment"]
    VALID_TRANSACTION_TYPES: List[str] = ["Credit", "Debit"]
    VALID_CHANNELS: List[str] = ["Online", "Mobile", "Branch", "ATM"]
    VALID_GENDERS: List[str] = ["M", "F", "Male", "Female"]
    VALID_STATUSES: List[str] = ["Active", "Inactive", "Suspended", "Closed"]
    VALID_RISK_SEGMENTS: List[str] = ["Low", "Medium", "High"]
    
    # Schema Definitions
    @staticmethod
    def get_customer_schema() -> StructType:
        """Returns the schema for customer data"""
        return StructType([
            StructField('customer_id', LongType(), False),
            StructField('name', StringType(), True),
            StructField('dob', StringType(), True),
            StructField('gender', StringType(), True),
            StructField('city', StringType(), True),
            StructField('join_date', StringType(), True),
            StructField('status', StringType(), True),
            StructField('email', StringType(), True),
            StructField('phone_number', StringType(), True),
            StructField('preferred_channel', StringType(), True),
            StructField('occupation', StringType(), True),
            StructField('income_range', StringType(), True),
            StructField('risk_segment', StringType(), True)
        ])
    
    @staticmethod
    def get_transactions_schema() -> StructType:
        """Returns the schema for transactions data"""
        return StructType([
            StructField('account_id', LongType(), False),
            StructField('customer_id', LongType(), False),
            StructField('account_type', StringType(), True),
            StructField('balance', DoubleType(), True),
            StructField('txn_id', LongType(), False),
            StructField('txn_date', StringType(), True),
            StructField('txn_type', StringType(), True),
            StructField('txn_amount', DoubleType(), True),
            StructField('txn_channel', StringType(), True)
        ])

# Global configuration instance
config = PipelineConfig()
