{"version": "0.2.0", "configurations": [{"name": "🐳 Run Docker Banking Pipeline", "type": "python", "request": "launch", "program": "${workspaceFolder}/docker_pipeline_runner.py", "console": "integratedTerminal", "env": {"PYTHONPATH": "${workspaceFolder}", "PYSPARK_PYTHON": "/opt/conda/bin/python", "PYSPARK_DRIVER_PYTHON": "/opt/conda/bin/python"}, "cwd": "${workspaceFolder}", "justMyCode": false}, {"name": "🧪 Test Docker Setup", "type": "python", "request": "launch", "program": "${workspaceFolder}/test_docker_setup.py", "console": "integratedTerminal", "env": {"PYTHONPATH": "${workspaceFolder}", "PYSPARK_PYTHON": "/opt/conda/bin/python", "PYSPARK_DRIVER_PYTHON": "/opt/conda/bin/python"}, "cwd": "${workspaceFolder}", "justMyCode": false}, {"name": "🥉 Debug Bronze Layer", "type": "python", "request": "launch", "program": "${workspaceFolder}/bronze/bronze_layer_pyspark.py", "console": "integratedTerminal", "env": {"PYTHONPATH": "${workspaceFolder}", "PYSPARK_PYTHON": "/opt/conda/bin/python", "PYSPARK_DRIVER_PYTHON": "/opt/conda/bin/python"}, "cwd": "${workspaceFolder}", "justMyCode": false}, {"name": "🥈 Debug Silver Layer", "type": "python", "request": "launch", "program": "${workspaceFolder}/silver/silver_layer_pyspark.py", "console": "integratedTerminal", "env": {"PYTHONPATH": "${workspaceFolder}", "PYSPARK_PYTHON": "/opt/conda/bin/python", "PYSPARK_DRIVER_PYTHON": "/opt/conda/bin/python"}, "cwd": "${workspaceFolder}", "justMyCode": false}, {"name": "🥇 Debug Gold Layer", "type": "python", "request": "launch", "program": "${workspaceFolder}/gold/gold_layer_pyspark.py", "console": "integratedTerminal", "env": {"PYTHONPATH": "${workspaceFolder}", "PYSPARK_PYTHON": "/opt/conda/bin/python", "PYSPARK_DRIVER_PYTHON": "/opt/conda/bin/python"}, "cwd": "${workspaceFolder}", "justMyCode": false}, {"name": "📊 Run Data Quality Tests", "type": "python", "request": "launch", "program": "${workspaceFolder}/tests/test_data_quality.py", "console": "integratedTerminal", "env": {"PYTHONPATH": "${workspaceFolder}", "PYSPARK_PYTHON": "/opt/conda/bin/python", "PYSPARK_DRIVER_PYTHON": "/opt/conda/bin/python"}, "cwd": "${workspaceFolder}", "justMyCode": false}]}