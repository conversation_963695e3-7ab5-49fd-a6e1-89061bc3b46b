{"version": "2.0.0", "tasks": [{"label": "Test Docker Setup", "type": "shell", "command": "python", "args": ["test_docker_setup.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "detail": "Test Docker environment and data accessibility"}, {"label": "Run Complete Banking Pipeline", "type": "shell", "command": "python", "args": ["docker_pipeline_runner.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "detail": "Execute the complete banking data pipeline"}, {"label": "Run Bronze Layer Only", "type": "shell", "command": "python", "args": ["-c", "from docker_pipeline_runner import DockerBankingPipeline; p = DockerBankingPipeline(); d = p.discover_data_files(); c = p.load_and_combine_data(d['customer_files'], 'customer'); t = p.load_and_combine_data(d['transaction_files'], 'transaction'); p.run_bronze_layer(c, t)"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "detail": "Run only the Bronze layer processing"}, {"label": "Check Data Files", "type": "shell", "command": "python", "args": ["-c", "from docker_pipeline_runner import DockerBankingPipeline; p = DockerBankingPipeline(); files = p.discover_data_files(); print(f'Customer files: {len(files[\"customer_files\"])}'); print(f'Transaction files: {len(files[\"transaction_files\"])}'); [print(f'  {f}') for f in files['customer_files'][:3]]; [print(f'  {f}') for f in files['transaction_files'][:3]]"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "detail": "Discover and list available data files"}, {"label": "Clean Output Directories", "type": "shell", "command": "rm", "args": ["-rf", "/home/<USER>/Volumes/dlt_banking_project_catalog/dlt_project_schema/bronze", "/home/<USER>/Volumes/dlt_banking_project_catalog/dlt_project_schema/silver", "/home/<USER>/Volumes/dlt_banking_project_catalog/dlt_project_schema/gold"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "detail": "Clean all output directories for fresh run"}, {"label": "View Pipeline Results", "type": "shell", "command": "python", "args": ["-c", "import os; base='/home/<USER>/Volumes/dlt_banking_project_catalog/dlt_project_schema'; [print(f'{layer}: {os.listdir(os.path.join(base, layer)) if os.path.exists(os.path.join(base, layer)) else \"Not found\"}') for layer in ['bronze', 'silver', 'gold']]"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "detail": "View generated pipeline output directories"}]}