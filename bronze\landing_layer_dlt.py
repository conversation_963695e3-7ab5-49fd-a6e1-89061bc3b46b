"""
Landing Layer - Delta Live Tables Implementation
Business Purpose: Raw data ingestion with schema enforcement and basic validation
This layer ensures data integrity from source systems into the lakehouse
"""

import dlt
from pyspark.sql.functions import *
from pyspark.sql.types import *
import sys
import os

# Add utilities to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'utilities'))
from config import config
from data_quality import DataQualityUtils

# ============================================================================
# CUSTOMER DATA LANDING LAYER
# ============================================================================

@dlt.table(
    name=config.LANDING_CUSTOMER_TABLE,
    comment="""
    Landing table for customer data ingestion.
    Business Purpose: Captures raw customer data from source systems for onboarding,
    KYC compliance, and customer lifecycle management.
    """,
    table_properties={
        "quality": "bronze",
        "pipelines.autoOptimize.managed": "true"
    }
)
def landing_customer_incremental():
    """
    Business Context: Ingests customer data from multiple source files
    Why: Banks need to onboard customers from various channels (online, branch, mobile)
    What: Raw customer demographics, contact info, and account preferences
    """
    return (
        spark.readStream
        .format("cloudFiles")
        .option("cloudFiles.format", "csv")
        .option("cloudFiles.schemaLocation", f"{config.CHECKPOINT_PATH}/customer_schema")
        .option("header", "true")
        .option("multiline", "true")
        .option("escape", '"')
        .schema(config.get_customer_schema())
        .load(f"{config.RAW_VOLUME_PATH}/customer/")
        .withColumn("_source_file", input_file_name())
        .withColumn("_ingestion_timestamp", current_timestamp())
        .withColumn("_ingestion_date", current_date())
    )

# ============================================================================
# ACCOUNTS & TRANSACTIONS DATA LANDING LAYER  
# ============================================================================

@dlt.table(
    name=config.LANDING_TRANSACTIONS_TABLE,
    comment="""
    Landing table for accounts and transactions data.
    Business Purpose: Captures all banking transactions for fraud detection,
    compliance reporting, and customer behavior analysis.
    """,
    table_properties={
        "quality": "bronze", 
        "pipelines.autoOptimize.managed": "true"
    }
)
def landing_accounts_transactions_incremental():
    """
    Business Context: Ingests transaction data from core banking systems
    Why: Real-time transaction monitoring for fraud detection and compliance
    What: Account balances, transaction details, and channel information
    """
    return (
        spark.readStream
        .format("cloudFiles")
        .option("cloudFiles.format", "csv")
        .option("cloudFiles.schemaLocation", f"{config.CHECKPOINT_PATH}/transactions_schema")
        .option("header", "true")
        .option("multiline", "true")
        .option("escape", '"')
        .schema(config.get_transactions_schema())
        .load(f"{config.RAW_VOLUME_PATH}/accounts_transactions/")
        .withColumn("_source_file", input_file_name())
        .withColumn("_ingestion_timestamp", current_timestamp())
        .withColumn("_ingestion_date", current_date())
    )

# ============================================================================
# DATA QUALITY MONITORING TABLES
# ============================================================================

@dlt.table(
    name="landing_data_quality_metrics",
    comment="""
    Tracks data quality metrics for landing layer monitoring.
    Business Purpose: Ensures data quality standards for regulatory compliance
    and operational excellence.
    """
)
def landing_data_quality_metrics():
    """
    Business Context: Monitor data quality for regulatory compliance
    Why: Banks must maintain high data quality for risk management and reporting
    What: Quality metrics, error counts, and data lineage tracking
    """
    customer_df = dlt.read_stream(config.LANDING_CUSTOMER_TABLE)
    transactions_df = dlt.read_stream(config.LANDING_TRANSACTIONS_TABLE)
    
    # Customer quality metrics
    customer_metrics = customer_df.agg(
        count("*").alias("total_customer_records"),
        sum(when(col("customer_id").isNull(), 1).otherwise(0)).alias("null_customer_ids"),
        sum(when(col("email").isNull() | (col("email") == ""), 1).otherwise(0)).alias("missing_emails"),
        sum(when(col("phone_number").isNull() | (col("phone_number") == ""), 1).otherwise(0)).alias("missing_phones")
    ).withColumn("table_name", lit("customers"))
    
    # Transaction quality metrics  
    transaction_metrics = transactions_df.agg(
        count("*").alias("total_transaction_records"),
        sum(when(col("txn_id").isNull(), 1).otherwise(0)).alias("null_transaction_ids"),
        sum(when(col("txn_amount").isNull(), 1).otherwise(0)).alias("null_amounts"),
        sum(when(col("txn_date").isNull(), 1).otherwise(0)).alias("null_dates")
    ).withColumn("table_name", lit("transactions"))
    
    return customer_metrics.unionByName(transaction_metrics, allowMissingColumns=True) \
                          .withColumn("measurement_timestamp", current_timestamp()) \
                          .withColumn("measurement_date", current_date())

# ============================================================================
# QUARANTINE TABLES FOR INVALID RECORDS
# ============================================================================

@dlt.table(
    name="landing_customer_quarantine",
    comment="""
    Quarantine table for customer records that fail basic validation.
    Business Purpose: Isolate problematic records for manual review and correction
    to maintain data quality standards.
    """
)
def landing_customer_quarantine():
    """
    Business Context: Quarantine invalid customer records for review
    Why: Prevent bad data from propagating downstream and affecting analytics
    What: Records with critical validation failures requiring manual intervention
    """
    return (
        dlt.read_stream(config.LANDING_CUSTOMER_TABLE)
        .filter(
            col("customer_id").isNull() |
            col("name").isNull() |
            (col("name") == "") |
            col("email").rlike(".*@\\.com$") |  # Invalid email patterns
            col("phone_number").rlike(".*[A-Za-z].*")  # Phone numbers with letters
        )
        .withColumn("quarantine_reason", 
            when(col("customer_id").isNull(), "Missing Customer ID")
            .when(col("name").isNull() | (col("name") == ""), "Missing Customer Name")
            .when(col("email").rlike(".*@\\.com$"), "Invalid Email Format")
            .when(col("phone_number").rlike(".*[A-Za-z].*"), "Invalid Phone Number")
            .otherwise("Multiple Issues")
        )
        .withColumn("quarantine_timestamp", current_timestamp())
    )
