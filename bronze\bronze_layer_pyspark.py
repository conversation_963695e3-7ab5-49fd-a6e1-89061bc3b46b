"""
Bronze Layer - PySpark Implementation
Business Purpose: Data cleansing, standardization, and quality enforcement using traditional PySpark
Alternative implementation for environments without Delta Live Tables
"""

from pyspark.sql import SparkSession, DataFrame
from pyspark.sql.functions import *
from pyspark.sql.types import *
from delta.tables import DeltaTable
import sys
import os

# Add utilities to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'utilities'))
from config import config
from data_quality import DataQualityUtils, BusinessRules

class BronzeLayerProcessor:
    """
    Bronze layer data processing using PySpark
    Business Purpose: Provide alternative implementation for bronze layer processing
    """
    
    def __init__(self, spark: SparkSession):
        self.spark = spark
        self.config = config
    
    def process_customer_data(self, input_path: str, output_path: str) -> None:
        """
        Business Context: Process customer data from landing to bronze layer
        Why: Clean and standardize customer data for downstream analytics
        What: Apply data quality rules and business transformations
        """
        
        print("Starting customer data processing...")
        print(f"Business Purpose: Standardizing customer data for KYC compliance and analytics")
        
        # Read raw customer data
        df = self.spark.read \
            .option("header", "true") \
            .option("multiline", "true") \
            .option("escape", '"') \
            .schema(config.get_customer_schema()) \
            .csv(input_path)
        
        print(f"Loaded {df.count()} raw customer records")
        
        # Apply data quality transformations
        df = self._clean_customer_data(df)
        
        # Apply business rules
        df = self._apply_customer_business_rules(df)
        
        # Add metadata
        df = df.withColumn("_processing_timestamp", current_timestamp()) \
               .withColumn("_processing_date", current_date())
        
        print(f"Processed {df.count()} clean customer records")
        
        # Write to Delta table
        self._write_delta_table(df, output_path, ["customer_id"], "customer_bronze")
        
        print("Customer data processing completed successfully")
    
    def process_transaction_data(self, input_path: str, output_path: str) -> None:
        """
        Business Context: Process transaction data from landing to bronze layer
        Why: Clean transaction data for fraud detection and compliance
        What: Apply validation rules and fraud detection flags
        """
        
        print("Starting transaction data processing...")
        print(f"Business Purpose: Cleaning transaction data for fraud detection and compliance")
        
        # Read raw transaction data
        df = self.spark.read \
            .option("header", "true") \
            .option("multiline", "true") \
            .option("escape", '"') \
            .schema(config.get_transactions_schema()) \
            .csv(input_path)
        
        print(f"Loaded {df.count()} raw transaction records")
        
        # Apply data quality transformations
        df = self._clean_transaction_data(df)
        
        # Apply business rules
        df = self._apply_transaction_business_rules(df)
        
        # Add metadata
        df = df.withColumn("_processing_timestamp", current_timestamp()) \
               .withColumn("_processing_date", current_date())
        
        print(f"Processed {df.count()} clean transaction records")
        
        # Write to Delta table
        self._write_delta_table(df, output_path, ["txn_id"], "transaction_bronze")
        
        print("Transaction data processing completed successfully")
    
    def _clean_customer_data(self, df: DataFrame) -> DataFrame:
        """
        Business Purpose: Apply data cleaning rules for customer data
        """
        
        print("Applying customer data cleaning rules...")
        
        # Standardize text fields
        df = df.select(
            col("customer_id"),
            DataQualityUtils.standardize_text_fields("name").alias("name"),
            col("dob"),
            DataQualityUtils.standardize_gender("gender").alias("standardized_gender"),
            DataQualityUtils.standardize_text_fields("city").alias("city"),
            col("join_date"),
            DataQualityUtils.standardize_status("status").alias("standardized_status"),
            lower(trim(col("email"))).alias("email"),
            DataQualityUtils.clean_phone_number("phone_number").alias("cleaned_phone_number"),
            DataQualityUtils.standardize_text_fields("preferred_channel").alias("standardized_channel"),
            DataQualityUtils.standardize_text_fields("occupation").alias("occupation"),
            DataQualityUtils.standardize_text_fields("income_range").alias("income_range"),
            DataQualityUtils.standardize_text_fields("risk_segment").alias("risk_segment")
        )
        
        # Calculate customer age
        df = df.withColumn("customer_age", DataQualityUtils.calculate_age("dob"))
        
        # Add data quality flags
        df = DataQualityUtils.add_data_quality_flags(df, "bronze_customer_pyspark")
        
        return df
    
    def _clean_transaction_data(self, df: DataFrame) -> DataFrame:
        """
        Business Purpose: Apply data cleaning rules for transaction data
        """
        
        print("Applying transaction data cleaning rules...")
        
        # Standardize fields
        df = df.select(
            col("account_id"),
            col("customer_id"),
            DataQualityUtils.standardize_text_fields("account_type").alias("account_type"),
            col("balance"),
            col("txn_id"),
            to_date(col("txn_date")).alias("txn_date"),
            DataQualityUtils.standardize_text_fields("txn_type").alias("txn_type"),
            col("txn_amount"),
            DataQualityUtils.standardize_text_fields("txn_channel").alias("txn_channel")
        )
        
        # Standardize transaction amounts
        df = df.withColumn("standardized_txn_amount",
            when(col("txn_type") == "DEBIT", -abs(col("txn_amount")))
            .when(col("txn_type") == "CREDIT", abs(col("txn_amount")))
            .otherwise(col("txn_amount"))
        )
        
        # Add transaction categorization
        df = df.withColumn("txn_category",
            when(abs(col("txn_amount")) <= 50, "SMALL")
            .when(abs(col("txn_amount")) <= 500, "MEDIUM")
            .when(abs(col("txn_amount")) <= 5000, "LARGE")
            .otherwise("VERY_LARGE")
        )
        
        # Add fraud detection flags
        df = df.withColumn("is_suspicious_amount", 
            DataQualityUtils.detect_suspicious_transactions("txn_amount"))
        
        # Add time-based features
        df = df.withColumn("txn_year", year(col("txn_date"))) \
               .withColumn("txn_month", month(col("txn_date"))) \
               .withColumn("txn_day_of_week", dayofweek(col("txn_date")))
        
        # Add data quality flags
        df = DataQualityUtils.add_data_quality_flags(df, "bronze_transaction_pyspark")
        
        return df
    
    def _apply_customer_business_rules(self, df: DataFrame) -> DataFrame:
        """
        Business Purpose: Apply customer-specific business validation rules
        """
        
        print("Applying customer business rules...")
        
        initial_count = df.count()
        
        # Validate required fields
        df = df.filter(
            col("customer_id").isNotNull() &
            col("name").isNotNull() &
            (trim(col("name")) != "") &
            col("email").isNotNull() &
            DataQualityUtils.validate_email("email") &
            col("cleaned_phone_number").isNotNull() &
            DataQualityUtils.validate_phone_number("cleaned_phone_number")
        )
        
        # Validate business rules
        df = df.filter(col("standardized_channel").isin(config.VALID_CHANNELS))
        df = df.filter(col("join_date") <= current_date())
        df = df.filter(col("customer_age") >= 18)  # Banking age requirement
        
        final_count = df.count()
        rejected_count = initial_count - final_count
        
        print(f"Business rules applied: {rejected_count} records rejected out of {initial_count}")
        
        return df
    
    def _apply_transaction_business_rules(self, df: DataFrame) -> DataFrame:
        """
        Business Purpose: Apply transaction-specific business validation rules
        """
        
        print("Applying transaction business rules...")
        
        initial_count = df.count()
        
        # Validate required fields
        df = df.filter(
            col("account_id").isNotNull() &
            col("customer_id").isNotNull() &
            col("txn_id").isNotNull() &
            col("txn_amount").isNotNull() &
            col("txn_date").isNotNull()
        )
        
        # Validate business rules
        df = df.filter(col("account_type").isin(config.VALID_ACCOUNT_TYPES))
        df = df.filter(col("txn_type").isin(config.VALID_TRANSACTION_TYPES))
        df = df.filter(col("txn_channel").isin(config.VALID_CHANNELS))
        df = df.filter(DataQualityUtils.validate_transaction_amount("txn_amount"))
        df = df.filter(col("txn_date") <= current_date())
        
        final_count = df.count()
        rejected_count = initial_count - final_count
        
        print(f"Business rules applied: {rejected_count} records rejected out of {initial_count}")
        
        return df
    
    def _write_delta_table(self, df: DataFrame, output_path: str, merge_keys: list, table_name: str) -> None:
        """
        Business Purpose: Write data to Delta table with ACID properties
        """
        
        print(f"Writing {table_name} to Delta table at {output_path}")
        
        try:
            # Check if table exists
            if DeltaTable.isDeltaTable(self.spark, output_path):
                print(f"Delta table exists, performing merge operation...")
                
                delta_table = DeltaTable.forPath(self.spark, output_path)
                
                # Build merge condition
                merge_condition = " AND ".join([f"target.{key} = source.{key}" for key in merge_keys])
                
                # Perform merge (upsert)
                delta_table.alias("target") \
                    .merge(df.alias("source"), merge_condition) \
                    .whenMatchedUpdateAll() \
                    .whenNotMatchedInsertAll() \
                    .execute()
                
                print(f"Merge completed for {table_name}")
            else:
                print(f"Creating new Delta table...")
                df.write \
                  .format("delta") \
                  .mode("overwrite") \
                  .option("mergeSchema", "true") \
                  .save(output_path)
                
                print(f"New Delta table created for {table_name}")
                
        except Exception as e:
            print(f"Error writing Delta table: {str(e)}")
            raise
    
    def generate_data_quality_report(self, customer_path: str, transaction_path: str) -> DataFrame:
        """
        Business Purpose: Generate data quality report for monitoring
        """
        
        print("Generating data quality report...")
        
        customer_df = self.spark.read.format("delta").load(customer_path)
        transaction_df = self.spark.read.format("delta").load(transaction_path)
        
        # Customer metrics
        customer_metrics = customer_df.agg(
            lit("customers").alias("table_name"),
            count("*").alias("total_records"),
            countDistinct("customer_id").alias("unique_records"),
            sum(when(col("standardized_status") == "ACTIVE", 1).otherwise(0)).alias("active_records"),
            avg("customer_age").alias("avg_age")
        )
        
        # Transaction metrics
        transaction_metrics = transaction_df.agg(
            lit("transactions").alias("table_name"),
            count("*").alias("total_records"),
            countDistinct("txn_id").alias("unique_records"),
            sum(when(col("is_suspicious_amount") == True, 1).otherwise(0)).alias("suspicious_records"),
            sum("standardized_txn_amount").alias("total_value")
        )
        
        report = customer_metrics.unionByName(transaction_metrics, allowMissingColumns=True) \
                                .withColumn("report_timestamp", current_timestamp())
        
        print("Data quality report generated successfully")
        
        return report

# Example usage function
def run_bronze_layer_processing(spark: SparkSession):
    """
    Business Purpose: Execute bronze layer processing pipeline
    """
    
    processor = BronzeLayerProcessor(spark)
    
    # Define paths (adjust based on your environment)
    customer_input_path = "/path/to/raw/customer/data"
    customer_output_path = "/path/to/bronze/customer/data"
    
    transaction_input_path = "/path/to/raw/transaction/data"
    transaction_output_path = "/path/to/bronze/transaction/data"
    
    # Process customer data
    processor.process_customer_data(customer_input_path, customer_output_path)
    
    # Process transaction data
    processor.process_transaction_data(transaction_input_path, transaction_output_path)
    
    # Generate quality report
    quality_report = processor.generate_data_quality_report(customer_output_path, transaction_output_path)
    quality_report.show()
    
    print("Bronze layer processing completed successfully!")
