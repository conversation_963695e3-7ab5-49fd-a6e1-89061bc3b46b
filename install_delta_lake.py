"""
Delta Lake Installation Script for Docker Environment
Business Purpose: Install and configure Delta Lake for the banking pipeline
"""

import subprocess
import sys
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def install_delta_lake():
    """
    Business Purpose: Install Delta Lake package for enhanced data lake capabilities
    """
    logger.info("🔧 Checking Delta Lake installation...")

    try:
        # Check if delta-spark is already installed
        result = subprocess.run([sys.executable, "-m", "pip", "show", "delta-spark"],
                              capture_output=True, text=True)

        if result.returncode == 0:
            logger.info("✅ Delta Lake is already installed")
            # Extract version info
            for line in result.stdout.split('\n'):
                if line.startswith('Version:'):
                    version = line.split(':')[1].strip()
                    logger.info(f"   Version: {version}")
                    break
            return True
        else:
            # Install delta-spark package
            logger.info("Installing delta-spark package...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "delta-spark"])
            logger.info("✅ Delta Lake installed successfully")
            return True

    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install Delta Lake: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error during installation: {e}")
        return False

def verify_delta_lake_installation():
    """
    Business Purpose: Verify Delta Lake is properly installed and configured
    """
    logger.info("🔍 Verifying Delta Lake installation...")
    
    try:
        # Test import
        import delta
        logger.info("✅ Delta Lake package imported successfully")
        
        # Test Spark with Delta
        from pyspark.sql import SparkSession
        
        spark = SparkSession.builder \
            .appName("Delta Lake Test") \
            .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
            .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
            .getOrCreate()
        
        logger.info("✅ Spark with Delta Lake extensions initialized")
        
        # Test basic Delta functionality
        test_df = spark.range(10)
        test_path = "/tmp/delta_test"
        
        # Clean up any existing test data
        import shutil
        if os.path.exists(test_path):
            shutil.rmtree(test_path)
        
        # Write as Delta table
        test_df.write.format("delta").mode("overwrite").save(test_path)
        
        # Read back
        read_df = spark.read.format("delta").load(test_path)
        count = read_df.count()
        
        logger.info(f"✅ Delta Lake functionality verified - Test count: {count}")
        
        # Clean up
        if os.path.exists(test_path):
            shutil.rmtree(test_path)
        
        spark.stop()
        return True
        
    except ImportError as e:
        logger.error(f"❌ Delta Lake import failed: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Delta Lake verification failed: {e}")
        return False

def check_java_version():
    """
    Business Purpose: Check if Java version is compatible with Delta Lake
    """
    logger.info("☕ Checking Java version compatibility...")
    
    try:
        result = subprocess.run(['java', '-version'], capture_output=True, text=True, stderr=subprocess.STDOUT)
        java_output = result.stderr if result.stderr else result.stdout
        
        logger.info(f"Java version info: {java_output.split(chr(10))[0]}")
        
        # Check for Java 8 or higher
        if 'version "1.8' in java_output or 'version "11' in java_output or 'version "17' in java_output:
            logger.info("✅ Java version is compatible with Delta Lake")
            return True
        else:
            logger.warning("⚠️  Java version may not be optimal for Delta Lake")
            return True  # Still allow installation
            
    except FileNotFoundError:
        logger.error("❌ Java not found. Please ensure Java is installed and in PATH")
        return False
    except Exception as e:
        logger.error(f"❌ Error checking Java version: {e}")
        return False

def show_delta_lake_benefits():
    """
    Business Purpose: Explain the business benefits of using Delta Lake
    """
    logger.info("\n" + "="*60)
    logger.info("💰 BUSINESS BENEFITS OF DELTA LAKE")
    logger.info("="*60)
    logger.info("🔄 ACID Transactions: Ensure data consistency and reliability")
    logger.info("⏰ Time Travel: Access historical data for auditing and compliance")
    logger.info("🔍 Schema Evolution: Adapt to changing business requirements")
    logger.info("🚀 Performance: Optimized queries and data skipping")
    logger.info("🛡️  Data Quality: Built-in data validation and constraints")
    logger.info("📊 Unified Batch & Streaming: Real-time and batch processing")
    logger.info("🔐 Security: Fine-grained access control and data governance")
    logger.info("="*60)

def main():
    """
    Business Purpose: Main installation and verification process
    """
    logger.info("🐳 DELTA LAKE INSTALLATION FOR DOCKER BANKING PIPELINE")
    logger.info("=" * 60)
    logger.info("Business Purpose: Enable advanced data lake capabilities for banking analytics")
    
    # Step 1: Check Java compatibility
    if not check_java_version():
        logger.error("❌ Java compatibility check failed. Please install Java 8 or higher.")
        return False
    
    # Step 2: Install Delta Lake
    if not install_delta_lake():
        logger.error("❌ Delta Lake installation failed.")
        return False
    
    # Step 3: Verify installation
    if not verify_delta_lake_installation():
        logger.error("❌ Delta Lake verification failed.")
        return False
    
    # Step 4: Show benefits
    show_delta_lake_benefits()
    
    logger.info("\n🎉 DELTA LAKE INSTALLATION COMPLETED SUCCESSFULLY!")
    logger.info("✅ Your banking pipeline now has enhanced data lake capabilities")
    logger.info("🚀 You can now run the complete pipeline with Delta Lake support")
    logger.info("\nNext steps:")
    logger.info("1. Run: python test_docker_setup.py")
    logger.info("2. Run: python docker_pipeline_runner.py")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("\n⚠️  Installation cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Installation failed with unexpected error: {e}")
        sys.exit(1)
