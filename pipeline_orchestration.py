"""
Banking Data Pipeline Orchestration
Business Purpose: Orchestrate the complete Medallion Architecture pipeline
Coordinates data flow from raw ingestion through gold layer analytics
"""

from pyspark.sql import SparkSession
from pyspark.sql.functions import *
import sys
import os
from typing import Dict, List
import logging

# Add utilities to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'utilities'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'bronze'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'silver'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'gold'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'tests'))

from config import config
from bronze_layer_pyspark import BronzeLayerProcessor
from silver_layer_pyspark import SilverLayerProcessor
from gold_layer_pyspark import GoldLayerProcessor
from test_data_quality import run_data_quality_tests

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BankingPipelineOrchestrator:
    """
    Complete pipeline orchestration for banking data
    Business Purpose: Manage end-to-end data pipeline execution
    """
    
    def __init__(self, spark: SparkSession, config_overrides: Dict = None):
        self.spark = spark
        self.config = config
        if config_overrides:
            for key, value in config_overrides.items():
                setattr(self.config, key, value)
        
        # Initialize processors
        self.bronze_processor = BronzeLayerProcessor(spark)
        self.silver_processor = SilverLayerProcessor(spark)
        self.gold_processor = GoldLayerProcessor(spark)
        
        logger.info("Banking Pipeline Orchestrator initialized")
    
    def run_full_pipeline(self, data_paths: Dict[str, str], run_tests: bool = True) -> Dict:
        """
        Business Context: Execute complete pipeline from raw data to gold analytics
        Why: Automate the entire data transformation process for operational efficiency
        What: Orchestrate bronze, silver, and gold layer processing with quality checks
        """
        
        logger.info("Starting full banking data pipeline execution")
        logger.info("Business Purpose: Transform raw banking data into analytics-ready insights")
        
        pipeline_results = {
            "pipeline_status": "RUNNING",
            "layers_completed": [],
            "test_results": None,
            "errors": []
        }
        
        try:
            # Step 1: Bronze Layer Processing
            logger.info("=== BRONZE LAYER PROCESSING ===")
            logger.info("Business Goal: Clean and validate raw banking data")
            
            self._process_bronze_layer(data_paths)
            pipeline_results["layers_completed"].append("bronze")
            logger.info("Bronze layer processing completed successfully")
            
            # Step 2: Silver Layer Processing
            logger.info("=== SILVER LAYER PROCESSING ===")
            logger.info("Business Goal: Enrich data with analytics features and customer context")
            
            self._process_silver_layer(data_paths)
            pipeline_results["layers_completed"].append("silver")
            logger.info("Silver layer processing completed successfully")
            
            # Step 3: Gold Layer Processing
            logger.info("=== GOLD LAYER PROCESSING ===")
            logger.info("Business Goal: Create business KPIs and executive analytics")
            
            self._process_gold_layer(data_paths)
            pipeline_results["layers_completed"].append("gold")
            logger.info("Gold layer processing completed successfully")
            
            # Step 4: Data Quality Testing
            if run_tests:
                logger.info("=== DATA QUALITY TESTING ===")
                logger.info("Business Goal: Validate data quality for compliance and accuracy")
                
                test_results = self._run_quality_tests(data_paths)
                pipeline_results["test_results"] = test_results
                logger.info("Data quality testing completed")
            
            pipeline_results["pipeline_status"] = "COMPLETED"
            logger.info("Full pipeline execution completed successfully")
            
        except Exception as e:
            logger.error(f"Pipeline execution failed: {str(e)}")
            pipeline_results["pipeline_status"] = "FAILED"
            pipeline_results["errors"].append(str(e))
            raise
        
        return pipeline_results
    
    def _process_bronze_layer(self, data_paths: Dict[str, str]) -> None:
        """
        Business Purpose: Process raw data into clean, validated bronze layer
        """
        
        logger.info("Processing customer data to bronze layer...")
        logger.info("Business Impact: Ensure customer data quality for KYC compliance")
        
        self.bronze_processor.process_customer_data(
            input_path=data_paths["raw_customer"],
            output_path=data_paths["bronze_customer"]
        )
        
        logger.info("Processing transaction data to bronze layer...")
        logger.info("Business Impact: Clean transaction data for fraud detection")
        
        self.bronze_processor.process_transaction_data(
            input_path=data_paths["raw_transaction"],
            output_path=data_paths["bronze_transaction"]
        )
    
    def _process_silver_layer(self, data_paths: Dict[str, str]) -> None:
        """
        Business Purpose: Create analytics-ready datasets with enriched features
        """
        
        logger.info("Enriching customer data for analytics...")
        logger.info("Business Impact: Enable customer segmentation and personalization")
        
        self.silver_processor.process_customer_enrichment(
            bronze_customer_path=data_paths["bronze_customer"],
            output_path=data_paths["silver_customer"]
        )
        
        logger.info("Enriching transaction data with customer context...")
        logger.info("Business Impact: Enable behavioral analytics and fraud detection")
        
        self.silver_processor.process_transaction_enrichment(
            bronze_transaction_path=data_paths["bronze_transaction"],
            bronze_customer_path=data_paths["bronze_customer"],
            output_path=data_paths["silver_transaction"]
        )
        
        logger.info("Creating account summaries...")
        logger.info("Business Impact: Enable relationship banking and portfolio management")
        
        self.silver_processor.process_account_summary(
            silver_transaction_path=data_paths["silver_transaction"],
            output_path=data_paths["silver_account"]
        )
    
    def _process_gold_layer(self, data_paths: Dict[str, str]) -> None:
        """
        Business Purpose: Create business KPIs and executive analytics
        """
        
        logger.info("Creating customer analytics for strategic insights...")
        logger.info("Business Impact: Enable data-driven customer strategy")
        
        self.gold_processor.process_customer_analytics(
            silver_customer_path=data_paths["silver_customer"],
            silver_account_path=data_paths["silver_account"],
            output_path=data_paths["gold_customer_analytics"]
        )
        
        logger.info("Creating transaction summaries for operational excellence...")
        logger.info("Business Impact: Monitor operational performance and efficiency")
        
        self.gold_processor.process_transaction_summary(
            silver_transaction_path=data_paths["silver_transaction"],
            output_path=data_paths["gold_transaction_summary"]
        )
        
        logger.info("Creating risk metrics for compliance...")
        logger.info("Business Impact: Ensure regulatory compliance and risk management")
        
        self.gold_processor.process_risk_metrics(
            silver_customer_path=data_paths["silver_customer"],
            silver_transaction_path=data_paths["silver_transaction"],
            output_path=data_paths["gold_risk_metrics"]
        )
        
        logger.info("Creating monthly KPIs for executive reporting...")
        logger.info("Business Impact: Enable strategic decision making")
        
        self.gold_processor.process_monthly_kpi(
            silver_customer_path=data_paths["silver_customer"],
            silver_transaction_path=data_paths["silver_transaction"],
            silver_account_path=data_paths["silver_account"],
            output_path=data_paths["gold_monthly_kpi"]
        )
    
    def _run_quality_tests(self, data_paths: Dict[str, str]) -> Dict:
        """
        Business Purpose: Validate data quality across all pipeline layers
        """
        
        logger.info("Running comprehensive data quality tests...")
        logger.info("Business Impact: Ensure data reliability for business decisions")
        
        test_table_paths = {
            "bronze_customer": data_paths["bronze_customer"],
            "bronze_transaction": data_paths["bronze_transaction"],
            "silver_customer": data_paths["silver_customer"],
            "silver_transaction": data_paths["silver_transaction"],
            "gold_customer_analytics": data_paths["gold_customer_analytics"],
            "gold_monthly_kpi": data_paths["gold_monthly_kpi"]
        }
        
        return run_data_quality_tests(self.spark, test_table_paths)
    
    def generate_pipeline_report(self, pipeline_results: Dict) -> str:
        """
        Business Purpose: Generate executive summary of pipeline execution
        """
        
        report = f"""
        ========================================
        BANKING DATA PIPELINE EXECUTION REPORT
        ========================================
        
        Pipeline Status: {pipeline_results['pipeline_status']}
        Layers Completed: {', '.join(pipeline_results['layers_completed'])}
        
        BUSINESS IMPACT SUMMARY:
        - Customer data cleaned and validated for KYC compliance
        - Transaction data processed for fraud detection and analytics
        - Customer insights generated for strategic decision making
        - Risk metrics calculated for regulatory compliance
        - Executive KPIs created for business monitoring
        
        """
        
        if pipeline_results.get("test_results"):
            test_summary = pipeline_results["test_results"]["test_summary"]
            report += f"""
        DATA QUALITY SUMMARY:
        - Total Tests: {test_summary['total_tests']}
        - Success Rate: {test_summary['success_rate']:.1f}%
        - Critical Failures: {test_summary['critical_failures']}
        
        """
            
            if test_summary['critical_failures'] > 0:
                report += "⚠️  ATTENTION REQUIRED: Critical data quality issues detected\n"
            else:
                report += "✅ All critical data quality checks passed\n"
        
        if pipeline_results.get("errors"):
            report += f"""
        ERRORS ENCOUNTERED:
        {chr(10).join(pipeline_results['errors'])}
        """
        
        return report

def create_sample_data_paths() -> Dict[str, str]:
    """
    Business Purpose: Define data paths for pipeline execution
    Modify these paths based on your environment
    """
    
    base_path = "/path/to/your/data"  # Modify this base path
    
    return {
        # Raw data paths
        "raw_customer": f"{base_path}/raw/customer/*.csv",
        "raw_transaction": f"{base_path}/raw/transaction/*.csv",
        
        # Bronze layer paths
        "bronze_customer": f"{base_path}/bronze/customer",
        "bronze_transaction": f"{base_path}/bronze/transaction",
        
        # Silver layer paths
        "silver_customer": f"{base_path}/silver/customer",
        "silver_transaction": f"{base_path}/silver/transaction",
        "silver_account": f"{base_path}/silver/account",
        
        # Gold layer paths
        "gold_customer_analytics": f"{base_path}/gold/customer_analytics",
        "gold_transaction_summary": f"{base_path}/gold/transaction_summary",
        "gold_risk_metrics": f"{base_path}/gold/risk_metrics",
        "gold_monthly_kpi": f"{base_path}/gold/monthly_kpi"
    }

def main():
    """
    Business Purpose: Main execution function for the banking data pipeline
    """
    
    # Initialize Spark session
    spark = SparkSession.builder \
        .appName("Banking Data Pipeline") \
        .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
        .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
        .getOrCreate()
    
    try:
        # Define data paths (modify based on your environment)
        data_paths = create_sample_data_paths()
        
        # Initialize orchestrator
        orchestrator = BankingPipelineOrchestrator(spark)
        
        # Run full pipeline
        results = orchestrator.run_full_pipeline(data_paths, run_tests=True)
        
        # Generate and print report
        report = orchestrator.generate_pipeline_report(results)
        print(report)
        
        logger.info("Banking data pipeline execution completed successfully")
        
    except Exception as e:
        logger.error(f"Pipeline execution failed: {str(e)}")
        raise
    finally:
        spark.stop()

if __name__ == "__main__":
    main()
