{"cells": [{"cell_type": "code", "execution_count": null, "id": "55b908aa", "metadata": {}, "outputs": [], "source": ["import dlt\n", "from pyspark.sql.functions import *\n", "from pyspark.sql.types import *"]}, {"cell_type": "markdown", "id": "c2d6e624", "metadata": {}, "source": ["## Customer Accounts - Bronze Layer"]}, {"cell_type": "code", "execution_count": null, "id": "683ee54f", "metadata": {}, "outputs": [], "source": ["\n", "@dlt.table(\n", "    name = \"bronze_customer_cleaned\"\n", "    comment= \"Cleaned customer data from the landing layer\"\n", ")\n", "@dlt.expect_or_fail(\"valid_customer_id\", \"customer_id IS NOT NULL\")\n", "@dlt.expect_or_drop(\"calid_customer_name\", \"name IS NOT NULL\")\n", "@dlt.expect_or_drop(\"valid_customer_dob\", \"dob IS NOT NULL\")\n", "@dlt.expect_or_drop(\"valid_customer_city\", \"city IS NOT NULL\")\n", "@dlt.expect_or_drop(\"valid_customer_join_date\", \"join_date IS NOT NULL\")\n", "@dlt.expect_or_drop(\"valid_customer_email\", \"email IS NOT NULL AND email RLIKE '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}$'\")\n", "@dlt.expect_or_drop(\"valid_customer_phone_number\", \"phone_number IS NOT NULL\")\n", "@dlt.expect_or_drop(\"valid_customer_channel\", \"channel IS NOT NULL\")\n", "@dlt.expect_or_drop(\"valid_customer_occupation\", \"occupation IS NOT NULL\")\n", "@dlt.expect_or_drop(\"valid_customer_income_range\", \"income_range IS NOT NULL\")\n", "@dlt.expect_or_drop(\"valid_customer_risk_segment\", \"risk_segment IS NOT NULL\")\n", "@dlr.expect_or_drop(\"valid_status\", \"status in NOT NULL\")\n", "@dlt.expect(\"valid_gender\", \"gender IS NOT NULL AND gender IN ('M', 'F')\")\n", "\n", "\n", "\n", "def bronze_customer_cleaned():\n", "    \n", "        dlt.read_stream(\"landing_customer_incremental\").select(\n", "            col(\"customer_id\"),\n", "            col(\"name\"),\n", "            col(\"dob\"),\n", "            col(\"gender\"),\n", "            col(\"city\"),\n", "            col(\"join_date\"),\n", "            col(\"status\"),\n", "            col(\"email\"),\n", "            col(\"phone_number\"),\n", "            col(\"channel\"),\n", "            col(\"occupation\"),\n", "            col(\"income_range\"),\n", "            col(\"risk_segment\")\n", "        )\n", "        \n", "        df = (df.withColumn('name', upper(col('name')))\n", "                .withColumn('email',lower(col('email')))\n", "                .withColumn('occupation', upper(col('occupation')))\n", "                .withColumn('city', upper(col('city')))\n", "                .withColumn('income_range', upper(col('income_range')))\n", "                .withColumn('preferred_channel', upper(col('channel')))\n", "        )\n", "        df = (df.withColumn('gender', \n", "                                    when(col('gender') == 'M', 'MALE')\n", "                                    .when(col('gender') == 'F', 'FEMALE').otherwise('UNKNOWN')))\n", "        \n", "\n", "        df = (df.withColumn(\"status\", when((col('status').isNull() | \n", "                                        trim(col('status')) == \"\"), 'ACTIVE').otherwise(col('status')))\n", "            \n", "        )\n", "\n", "        df = df.withColumn(\"phone_number\", trim(col(\"phone_number\")))\n", "        df = df.withColumn(\"phone_number\", regexp_replace(col(\"phone_number\"), r\"[^0-9]\", \"\"))\n", "        df = df.filter(col(\"phone_number\").rlike(r\"^\\d{10}$\"))\n", "\n", "        df = df.filter(col(\"preffered_channel\").isin('ONLINE','MOBILE','BRANCH','ATM'))\n", "\n", "        return df"]}, {"cell_type": "markdown", "id": "cd6e282a", "metadata": {}, "source": ["### Account_transactions - <PERSON> Layer"]}, {"cell_type": "code", "execution_count": null, "id": "68b23796", "metadata": {}, "outputs": [], "source": ["@dlt.table(\n", "    name=\"bronze_accounts_transactions_bronze\",\n", "    comment=\"Bronze layer for accounts transactions data\"\n", ")\n", "@dlt.expect_or_fail(\"valid_account_id\", \"account_id IS NOT NULL\")\n", "@dlt.expect_or_drop(\"valid_customer_id\", \"customer_id IS NOT NULL\")\n", "@dlt.expect_or_drop(\"valid_account_type\", \"account_type IS NOT NULL\")\n", "@dlt.expect_or_drop(\"valid_balance\", \"balance IS NOT NULL\")\n", "@dlt.expect_or_fail(\"valid_txn_id\", \"txn_id IS NOT NULL\")\n", "@dlt.expect_or_drop(\"valid_txn_date\", \"txn_date IS NOT NULL\")\n", "@dlt.expect_or_drop(\"valid_txn_type\", \"txn_type IS NOT NULL\")\n", "@dlt.expect_or_drop(\"valid_txn_amount\", \"txn_amount IS NOT NULL\")\n", "@dlt.expect_or_drop(\"valid_txn_channel\", \"txn_channel IS NOT NULL\")\n", "def bronze_accounts_transactions_bronze():\n", "    df = dlt.read_stream(\"landing_accounts_transactions_incremental\")\n", "\n", "\n", "    return df\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}