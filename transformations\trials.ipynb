from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
spark = SparkSession.builder.appName("Accounts Analysis Spark").getOrCreate()
spark


from pathlib import Path
from pyspark.sql.functions import *
from pyspark.sql.types import *
# Define data path (cross-platform safe)
# data_path = Path(r"E:\Study Space\Data\TPCH_SF10\customer.parquet")
data_path = r"E:\Study Space\Analytics Enginerring\Data Engineering\Azure Databricks\Lakeflow Declarative Pipeline\DLT_BANKING_PROJECT\data\accounts_transactions_2023_set1.csv"

accounts_schema = StructType(
    [
        StructField('account_id', LongType(), True), 
        StructField('customer_id', LongType(), True), 
        StructField('account_type', StringType(), True), 
        StructField('balance', DoubleType(), True), 
        StructField('txn_id', LongType(), True), 
        <PERSON>ructField('txn_date', DateType(), True), 
        <PERSON>ruct<PERSON>ield('txn_type', StringType(), True), 
        StructField('txn_amount', DoubleType(), True), 
        StructField('txn_channel', StringType(), True)
        ])

# Load customer data from Parquet
accounts_df = (
    spark.read
        .option('header', 'true') 
        .schema(accounts_schema)
        .csv(data_path)
)

# Display schema and sample records
accounts_df.printSchema()
accounts_df.show(10, truncate=False)