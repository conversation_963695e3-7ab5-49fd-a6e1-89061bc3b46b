{"cells": [{"cell_type": "code", "execution_count": 2, "id": "b816dad8", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <div>\n", "                <p><b>SparkSession - in-memory</b></p>\n", "                \n", "        <div>\n", "            <p><b>SparkContext</b></p>\n", "\n", "            <p><a href=\"http://SaiSunrisers:4041\">Spark UI</a></p>\n", "\n", "            <dl>\n", "              <dt>Version</dt>\n", "                <dd><code>v3.5.6</code></dd>\n", "              <dt>Master</dt>\n", "                <dd><code>local[*]</code></dd>\n", "              <dt>AppName</dt>\n", "                <dd><code>Accounts Analysis Spark</code></dd>\n", "            </dl>\n", "        </div>\n", "        \n", "            </div>\n", "        "], "text/plain": ["<pyspark.sql.session.SparkSession at 0x1cbe9d44dd0>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from pyspark.sql import SparkSession\n", "from pyspark.sql.functions import *\n", "from pyspark.sql.types import *\n", "spark = SparkSession.builder.appName(\"Accounts Analysis Spark\").getOrCreate()\n", "spark\n"]}, {"cell_type": "code", "execution_count": 3, "id": "2f41ee59", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- account_id: long (nullable = true)\n", " |-- customer_id: long (nullable = true)\n", " |-- account_type: string (nullable = true)\n", " |-- balance: double (nullable = true)\n", " |-- txn_id: long (nullable = true)\n", " |-- txn_date: date (nullable = true)\n", " |-- txn_type: string (nullable = true)\n", " |-- txn_amount: double (nullable = true)\n", " |-- txn_channel: string (nullable = true)\n", "\n", "+----------+-----------+------------+--------+------+----------+--------+----------+-----------+\n", "|account_id|customer_id|account_type|balance |txn_id|txn_date  |txn_type|txn_amount|txn_channel|\n", "+----------+-----------+------------+--------+------+----------+--------+----------+-----------+\n", "|100010    |1          |Current     |500.0   |3     |2023-07-28|Debit   |-11.22    |Branch     |\n", "|100010    |1          |Current     |600.0   |1     |2023-09-02|Debit   |-285.74   |ATM        |\n", "|100010    |1          |Current     |700.0   |2     |2023-09-25|Debit   |258.57    |Online     |\n", "|100010    |1          |Current     |800.0   |4     |2023-12-08|Debit   |-87.41    |Mobile     |\n", "|100011    |1          |Savings     |3453.22 |5     |2023-07-14|Debit   |1.0       |Mobile     |\n", "|100011    |1          |Savings     |3453.22 |6     |2023-07-22|Debit   |1.0       |Mobile     |\n", "|100012    |1          |Current     |-1022.97|7     |2022-01-16|Credit  |172.07    |Branch     |\n", "|100012    |1          |Current     |-1022.97|9     |2023-03-28|Debit   |-12.51    |Online     |\n", "|100012    |1          |Current     |-1022.97|8     |2023-11-18|Credit  |78.02     |ATM        |\n", "|100020    |2          |Savings     |-229.6  |14    |2023-02-06|Debit   |-35.35    |Online     |\n", "+----------+-----------+------------+--------+------+----------+--------+----------+-----------+\n", "only showing top 10 rows\n", "\n"]}], "source": ["from pathlib import Path\n", "from pyspark.sql.functions import *\n", "from pyspark.sql.types import *\n", "# Define data path (cross-platform safe)\n", "# data_path = Path(r\"E:\\Study Space\\Data\\TPCH_SF10\\customer.parquet\")\n", "data_path = r\"E:\\Study Space\\Analytics Enginerring\\Data Engineering\\Azure Databricks\\Lakeflow Declarative Pipeline\\DLT_BANKING_PROJECT\\data\\accounts_transactions_2023_set1.csv\"\n", "\n", "accounts_schema = StructType(\n", "    [\n", "        StructField('account_id', LongType(), True), \n", "        StructField('customer_id', LongType(), True), \n", "        StructField('account_type', StringType(), True), \n", "        StructField('balance', DoubleType(), True), \n", "        StructField('txn_id', LongType(), True), \n", "        StructField('txn_date', DateType(), True), \n", "        StructField('txn_type', StringType(), True), \n", "        StructField('txn_amount', DoubleType(), True), \n", "        StructField('txn_channel', StringType(), True)\n", "        ])\n", "\n", "# Load customer data from Parquet\n", "accounts_df = (\n", "    spark.read\n", "        .option('header', 'true') \n", "        .schema(accounts_schema)\n", "        .csv(data_path)\n", ")\n", "\n", "# Display schema and sample records\n", "accounts_df.printSchema()\n", "accounts_df.show(10, truncate=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}