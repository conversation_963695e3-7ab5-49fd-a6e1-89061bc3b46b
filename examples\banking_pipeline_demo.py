"""
Banking Pipeline Demo - Working with Your Data
Business Purpose: Demonstrate how to use the Medallion Architecture pipeline with your banking data
This script shows practical examples using your customer and transaction data
"""

from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
import sys
import os

# Add project paths
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'utilities'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'bronze'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'silver'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'gold'))

from config import config
from bronze_layer_pyspark import BronzeLayerProcessor
from silver_layer_pyspark import SilverLayerProcessor
from gold_layer_pyspark import GoldLayerProcessor

def initialize_spark():
    """
    Business Purpose: Initialize Spark session for banking data processing
    """
    return SparkSession.builder \
        .appName("Banking Pipeline Demo") \
        .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
        .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
        .getOrCreate()

def explore_your_data(spark):
    """
    Business Context: Explore your existing banking data to understand structure and quality
    Why: Understanding data characteristics helps optimize pipeline configuration
    What: Analyze customer and transaction data patterns, quality issues, and business insights
    """
    
    print("=== EXPLORING YOUR BANKING DATA ===")
    print("Business Purpose: Understanding data characteristics for pipeline optimization")
    
    # Load your customer data
    customer_files = [
        "data/customers_2023_set1.csv",
        "data/customers_2023_set2.csv", 
        "data/customers_2024_set1.csv",
        "data/customers_2024_set2.csv"
    ]
    
    # Load your transaction data
    transaction_files = [
        "data/accounts_transactions_2023_set1.csv",
        "data/accounts_transactions_2023_set2.csv",
        "data/accounts_transactions_2024_set1.csv", 
        "data/accounts_transactions_2024_set2.csv"
    ]
    
    print("\n--- CUSTOMER DATA ANALYSIS ---")
    
    # Combine all customer files
    customer_df = None
    for file in customer_files:
        if os.path.exists(file):
            df = spark.read.option("header", "true").csv(file)
            customer_df = df if customer_df is None else customer_df.union(df)
    
    if customer_df:
        print(f"Total customer records: {customer_df.count()}")
        print(f"Unique customers: {customer_df.select('customer_id').distinct().count()}")
        
        # Business Insight: Customer demographics
        print("\nCustomer Demographics:")
        customer_df.groupBy("gender").count().show()
        customer_df.groupBy("city").count().orderBy(desc("count")).show(10)
        customer_df.groupBy("risk_segment").count().show()
        
        # Business Insight: Data quality issues
        print("\nData Quality Issues:")
        print(f"Missing emails: {customer_df.filter(col('email').isNull() | (col('email') == '')).count()}")
        print(f"Invalid phone numbers: {customer_df.filter(col('phone_number').rlike('.*[A-Za-z].*')).count()}")
        print(f"Missing gender: {customer_df.filter(col('gender').isNull() | (col('gender') == '')).count()}")
    
    print("\n--- TRANSACTION DATA ANALYSIS ---")
    
    # Combine all transaction files
    transaction_df = None
    for file in transaction_files:
        if os.path.exists(file):
            df = spark.read.option("header", "true").csv(file)
            transaction_df = df if transaction_df is None else transaction_df.union(df)
    
    if transaction_df:
        print(f"Total transaction records: {transaction_df.count()}")
        print(f"Unique transactions: {transaction_df.select('txn_id').distinct().count()}")
        
        # Business Insight: Transaction patterns
        print("\nTransaction Patterns:")
        transaction_df.groupBy("txn_type").count().show()
        transaction_df.groupBy("txn_channel").count().show()
        transaction_df.groupBy("account_type").count().show()
        
        # Business Insight: Financial metrics
        print("\nFinancial Metrics:")
        transaction_df.select(
            avg("txn_amount").alias("avg_transaction"),
            sum("txn_amount").alias("total_volume"),
            min("txn_amount").alias("min_transaction"),
            max("txn_amount").alias("max_transaction")
        ).show()
        
        # Business Insight: Potential issues
        print("\nPotential Issues:")
        print(f"Negative balances: {transaction_df.filter(col('balance') < 0).count()}")
        print(f"Zero amount transactions: {transaction_df.filter(col('txn_amount') == 0).count()}")
    
    return customer_df, transaction_df

def demonstrate_bronze_processing(spark, customer_df, transaction_df):
    """
    Business Context: Demonstrate bronze layer processing with your data
    Why: Show how raw data is cleaned and validated for downstream analytics
    What: Apply data quality rules and business validations to your banking data
    """
    
    print("\n=== BRONZE LAYER PROCESSING DEMO ===")
    print("Business Purpose: Clean and validate raw banking data for compliance")
    
    processor = BronzeLayerProcessor(spark)
    
    # Save sample data as CSV for processing
    customer_df.coalesce(1).write.mode("overwrite").option("header", "true").csv("temp/raw_customer")
    transaction_df.coalesce(1).write.mode("overwrite").option("header", "true").csv("temp/raw_transaction")
    
    # Process through bronze layer
    print("\nProcessing customer data through bronze layer...")
    processor.process_customer_data("temp/raw_customer", "temp/bronze_customer")
    
    print("\nProcessing transaction data through bronze layer...")
    processor.process_transaction_data("temp/raw_transaction", "temp/bronze_transaction")
    
    # Show results
    bronze_customer = spark.read.format("delta").load("temp/bronze_customer")
    bronze_transaction = spark.read.format("delta").load("temp/bronze_transaction")
    
    print(f"\nBronze Customer Records: {bronze_customer.count()}")
    print(f"Bronze Transaction Records: {bronze_transaction.count()}")
    
    # Business Insight: Data quality improvements
    print("\nData Quality Improvements:")
    bronze_customer.select("standardized_gender", "digital_adoption_score", "customer_age").show(5)
    bronze_transaction.select("txn_category", "is_suspicious_amount", "standardized_txn_amount").show(5)
    
    return bronze_customer, bronze_transaction

def demonstrate_silver_processing(spark, bronze_customer, bronze_transaction):
    """
    Business Context: Demonstrate silver layer enrichment with customer context
    Why: Show how data is enriched for advanced analytics and business insights
    What: Add customer context to transactions and calculate analytical features
    """
    
    print("\n=== SILVER LAYER PROCESSING DEMO ===")
    print("Business Purpose: Enrich data with analytics features and customer context")
    
    processor = SilverLayerProcessor(spark)
    
    # Process through silver layer
    print("\nEnriching customer data...")
    processor.process_customer_enrichment("temp/bronze_customer", "temp/silver_customer")
    
    print("\nEnriching transaction data with customer context...")
    processor.process_transaction_enrichment("temp/bronze_transaction", "temp/bronze_customer", "temp/silver_transaction")
    
    print("\nCreating account summaries...")
    processor.process_account_summary("temp/silver_transaction", "temp/silver_account")
    
    # Show results
    silver_customer = spark.read.format("delta").load("temp/silver_customer")
    silver_transaction = spark.read.format("delta").load("temp/silver_transaction")
    silver_account = spark.read.format("delta").load("temp/silver_account")
    
    print(f"\nSilver Customer Records: {silver_customer.count()}")
    print(f"Silver Transaction Records: {silver_transaction.count()}")
    print(f"Silver Account Records: {silver_account.count()}")
    
    # Business Insight: Enriched features
    print("\nCustomer Enrichment Features:")
    silver_customer.select("customer_id", "age_segment", "customer_tenure_category", "digital_adoption_score").show(5)
    
    print("\nTransaction Enrichment Features:")
    silver_transaction.select("txn_id", "customer_name", "transaction_risk_score", "channel_consistency_flag").show(5)
    
    print("\nAccount Summary Metrics:")
    silver_account.select("account_id", "customer_value_segment", "account_health_score", "total_transactions").show(5)
    
    return silver_customer, silver_transaction, silver_account

def demonstrate_gold_processing(spark):
    """
    Business Context: Demonstrate gold layer business analytics and KPIs
    Why: Show how business metrics are calculated for executive reporting
    What: Create customer analytics, risk metrics, and monthly KPIs
    """
    
    print("\n=== GOLD LAYER PROCESSING DEMO ===")
    print("Business Purpose: Create business KPIs and executive analytics")
    
    processor = GoldLayerProcessor(spark)
    
    # Process through gold layer
    print("\nCreating customer analytics...")
    processor.process_customer_analytics("temp/silver_customer", "temp/silver_account", "temp/gold_customer_analytics")
    
    print("\nCreating transaction summaries...")
    processor.process_transaction_summary("temp/silver_transaction", "temp/gold_transaction_summary")
    
    print("\nCreating risk metrics...")
    processor.process_risk_metrics("temp/silver_customer", "temp/silver_transaction", "temp/gold_risk_metrics")
    
    print("\nCreating monthly KPIs...")
    processor.process_monthly_kpi("temp/silver_customer", "temp/silver_transaction", "temp/silver_account", "temp/gold_monthly_kpi")
    
    # Show results
    gold_customer_analytics = spark.read.format("delta").load("temp/gold_customer_analytics")
    gold_transaction_summary = spark.read.format("delta").load("temp/gold_transaction_summary")
    gold_monthly_kpi = spark.read.format("delta").load("temp/gold_monthly_kpi")
    
    print(f"\nGold Customer Analytics Records: {gold_customer_analytics.count()}")
    print(f"Gold Transaction Summary Records: {gold_transaction_summary.count()}")
    print(f"Gold Monthly KPI Records: {gold_monthly_kpi.count()}")
    
    # Business Insight: Executive metrics
    print("\nCustomer Analytics Insights:")
    gold_customer_analytics.select("customer_id", "estimated_clv", "engagement_score", "churn_risk_score").show(5)
    
    print("\nTransaction Performance Metrics:")
    gold_transaction_summary.select("txn_date", "txn_channel", "transaction_count", "channel_performance_score").show(5)
    
    print("\nMonthly Business KPIs:")
    gold_monthly_kpi.select("kpi_month", "new_customers", "total_revenue", "digital_adoption_rate").show(5)

def generate_business_insights(spark):
    """
    Business Context: Generate actionable business insights from processed data
    Why: Demonstrate the business value of the pipeline outputs
    What: Create executive summary with key findings and recommendations
    """
    
    print("\n=== BUSINESS INSIGHTS SUMMARY ===")
    print("Business Purpose: Extract actionable insights for strategic decision making")
    
    try:
        # Load gold layer data
        customer_analytics = spark.read.format("delta").load("temp/gold_customer_analytics")
        monthly_kpi = spark.read.format("delta").load("temp/gold_monthly_kpi")
        
        print("\n--- KEY BUSINESS INSIGHTS ---")
        
        # Customer insights
        high_value_customers = customer_analytics.filter(col("customer_value_segment") == "HIGH_VALUE").count()
        high_churn_risk = customer_analytics.filter(col("churn_risk_score") >= 4).count()
        digital_ready = customer_analytics.filter(col("digital_readiness_score") >= 8).count()
        
        print(f"High Value Customers: {high_value_customers}")
        print(f"High Churn Risk Customers: {high_churn_risk}")
        print(f"Digital Ready Customers: {digital_ready}")
        
        # Business recommendations
        print("\n--- BUSINESS RECOMMENDATIONS ---")
        
        if high_churn_risk > 0:
            print(f"🔴 URGENT: {high_churn_risk} customers at high churn risk - implement retention campaigns")
        
        if digital_ready > 0:
            print(f"🟢 OPPORTUNITY: {digital_ready} customers ready for digital services - promote mobile/online banking")
        
        if high_value_customers > 0:
            print(f"💰 FOCUS: {high_value_customers} high-value customers - prioritize relationship management")
        
        # Performance metrics
        if monthly_kpi.count() > 0:
            latest_kpi = monthly_kpi.orderBy(desc("kpi_month")).first()
            print(f"\n--- LATEST PERFORMANCE METRICS ---")
            print(f"Monthly Revenue: ${latest_kpi['total_revenue']:,.2f}")
            print(f"New Customers: {latest_kpi['new_customers']}")
            print(f"Digital Adoption Rate: {latest_kpi['digital_adoption_rate']:.1f}%")
        
    except Exception as e:
        print(f"Could not generate insights: {str(e)}")

def main():
    """
    Business Purpose: Complete demonstration of the banking data pipeline
    """
    
    print("BANKING DATA PIPELINE DEMONSTRATION")
    print("===================================")
    print("Business Goal: Transform raw banking data into actionable business insights")
    
    # Initialize Spark
    spark = initialize_spark()
    
    try:
        # Step 1: Explore your existing data
        customer_df, transaction_df = explore_your_data(spark)
        
        if customer_df and transaction_df:
            # Step 2: Bronze layer processing
            bronze_customer, bronze_transaction = demonstrate_bronze_processing(spark, customer_df, transaction_df)
            
            # Step 3: Silver layer processing
            silver_customer, silver_transaction, silver_account = demonstrate_silver_processing(spark, bronze_customer, bronze_transaction)
            
            # Step 4: Gold layer processing
            demonstrate_gold_processing(spark)
            
            # Step 5: Generate business insights
            generate_business_insights(spark)
            
            print("\n=== PIPELINE DEMONSTRATION COMPLETED ===")
            print("✅ Successfully processed your banking data through all medallion layers")
            print("📊 Business insights and KPIs are now available for decision making")
            print("🔍 Review the generated Delta tables for detailed analytics")
        
        else:
            print("❌ Could not load your data files. Please check the file paths.")
    
    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")
        raise
    
    finally:
        spark.stop()

if __name__ == "__main__":
    main()
