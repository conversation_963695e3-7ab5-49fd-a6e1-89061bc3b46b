# Docker PySpark Setup Guide for Banking Pipeline

## 🐳 Running Banking Pipeline in Docker with VS Code

This guide shows you how to connect VS Code to your Docker PySpark environment and run the banking data pipeline.

## Prerequisites

- Docker Desktop installed and running
- VS Code with Remote-Containers extension
- Your PySpark Docker container running
- Data files in the correct directory structure

## Your Current Data Structure

Based on your setup, your data is organized as:
```
/home/<USER>/Volumes/dlt_banking_project_catalog/dlt_project_schema/raw/
├── 2023/
│   ├── customers/
│   │   ├── customers_2023_set1.csv
│   │   └── customers_2023_set2.csv
│   └── accounts/
│       ├── accounts_transactions_2023_set1.csv
│       └── accounts_transactions_2023_set2.csv
└── 2024/
    ├── customers/
    │   ├── customers_2024_set1.csv
    │   └── customers_2024_set2.csv
    └── accounts/
        ├── accounts_transactions_2024_set1.csv
        └── accounts_transactions_2024_set2.csv
```

## Step 1: Connect VS Code to Docker Container

### Option A: Using VS Code Remote-Containers Extension

1. **Install Remote-Containers Extension**:
   - Open VS Code
   - Go to Extensions (Ctrl+Shift+X)
   - Search for "Remote - Containers"
   - Install the extension

2. **Connect to Running Container**:
   - Press `Ctrl+Shift+P` to open command palette
   - Type "Remote-Containers: Attach to Running Container"
   - Select your PySpark container from the list

3. **Open Project Directory**:
   - Once connected, open the project directory in the container
   - Navigate to `/home/<USER>/` or your project location

### Option B: Using Docker Exec (Alternative)

1. **Find your container name**:
   ```bash
   docker ps
   ```

2. **Connect to container**:
   ```bash
   docker exec -it <container_name> /bin/bash
   ```

3. **Navigate to project directory**:
   ```bash
   cd /home/<USER>/
   ```

## Step 2: Setup Project in Container

1. **Clone/Copy your project files** to the container:
   ```bash
   # If using git
   git clone <your-repo-url>
   
   # Or copy files from host
   docker cp /path/to/your/project <container_name>:/home/<USER>/banking_pipeline
   ```

2. **Install required Python packages** (if not already installed):
   ```bash
   pip install delta-spark pytest
   ```

3. **Verify PySpark installation**:
   ```bash
   python -c "from pyspark.sql import SparkSession; print('PySpark is working!')"
   ```

## Step 3: Configure Project for Docker Environment

The project has been pre-configured for your Docker environment with these settings:

### Updated Configuration (`utilities/config.py`):
```python
# Docker Environment Configuration
DOCKER_BASE_PATH: str = "/home/<USER>/Volumes/dlt_banking_project_catalog/dlt_project_schema"

# Data Source Paths (Year-wise structure)
RAW_CUSTOMER_2023_PATH: str = f"{RAW_VOLUME_PATH}/2023/customers"
RAW_CUSTOMER_2024_PATH: str = f"{RAW_VOLUME_PATH}/2024/customers"
RAW_ACCOUNTS_2023_PATH: str = f"{RAW_VOLUME_PATH}/2023/accounts"
RAW_ACCOUNTS_2024_PATH: str = f"{RAW_VOLUME_PATH}/2024/accounts"
```

## Step 4: Run the Banking Pipeline

### Quick Start - Complete Pipeline:
```bash
# Navigate to project directory
cd /home/<USER>/banking_pipeline

# Run the complete pipeline
python docker_pipeline_runner.py
```

### Step-by-Step Execution:

1. **Test Data Discovery**:
   ```python
   from docker_pipeline_runner import DockerBankingPipeline
   
   pipeline = DockerBankingPipeline()
   data_files = pipeline.discover_data_files()
   print(f"Found {len(data_files['customer_files'])} customer files")
   print(f"Found {len(data_files['transaction_files'])} transaction files")
   ```

2. **Run Individual Layers**:
   ```python
   # Bronze layer only
   pipeline = DockerBankingPipeline()
   data_files = pipeline.discover_data_files()
   customer_path = pipeline.load_and_combine_data(data_files["customer_files"], "customer")
   transaction_path = pipeline.load_and_combine_data(data_files["transaction_files"], "transaction")
   bronze_paths = pipeline.run_bronze_layer(customer_path, transaction_path)
   ```

## Step 5: VS Code Development Setup

### Create VS Code Workspace Settings

Create `.vscode/settings.json` in your project:
```json
{
    "python.defaultInterpreterPath": "/opt/conda/bin/python",
    "python.terminal.activateEnvironment": false,
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true
    },
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true
}
```

### Create Launch Configuration

Create `.vscode/launch.json`:
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Run Banking Pipeline",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/docker_pipeline_runner.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        {
            "name": "Debug Bronze Layer",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/bronze/bronze_layer_pyspark.py",
            "console": "integratedTerminal"
        }
    ]
}
```

## Step 6: Verify Installation and Run

### 1. Check Data Files:
```bash
# Verify your data files are accessible
ls -la /home/<USER>/Volumes/dlt_banking_project_catalog/dlt_project_schema/raw/2024/customers/
ls -la /home/<USER>/Volumes/dlt_banking_project_catalog/dlt_project_schema/raw/2024/accounts/
```

### 2. Test Spark Connection:
```python
from pyspark.sql import SparkSession

spark = SparkSession.builder \
    .appName("Test") \
    .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
    .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
    .getOrCreate()

print("Spark version:", spark.version)
spark.stop()
```

### 3. Run Complete Pipeline:
```bash
python docker_pipeline_runner.py
```

## Expected Output Structure

After running the pipeline, you'll have:
```
/home/<USER>/Volumes/dlt_banking_project_catalog/dlt_project_schema/
├── raw/                    # Your original data
├── bronze/                 # Cleaned and validated data
│   ├── customer/
│   └── transaction/
├── silver/                 # Enriched analytics-ready data
│   ├── customer/
│   ├── transaction/
│   └── account/
├── gold/                   # Business KPIs and analytics
│   ├── customer_analytics/
│   ├── transaction_summary/
│   ├── risk_metrics/
│   └── monthly_kpi/
└── checkpoints/           # Spark checkpoints
```

## Troubleshooting

### Common Issues:

1. **Permission Errors**:
   ```bash
   # Fix permissions
   sudo chown -R jovyan:users /home/<USER>/Volumes/
   ```

2. **Memory Issues**:
   ```bash
   # Increase Docker memory allocation in Docker Desktop settings
   # Or reduce Spark parallelism
   export SPARK_DRIVER_MEMORY=2g
   export SPARK_EXECUTOR_MEMORY=2g
   ```

3. **Path Issues**:
   ```python
   # Verify paths in Python
   import os
   print(os.path.exists("/home/<USER>/Volumes/dlt_banking_project_catalog/dlt_project_schema/raw/2024/customers"))
   ```

4. **Delta Lake Issues**:
   ```bash
   # Ensure Delta Lake is properly installed
   pip install delta-spark==2.4.0
   ```

## Performance Optimization

### For Large Datasets:
```python
# Optimize Spark configuration
spark.conf.set("spark.sql.adaptive.enabled", "true")
spark.conf.set("spark.sql.adaptive.coalescePartitions.enabled", "true")
spark.conf.set("spark.sql.adaptive.skewJoin.enabled", "true")
```

### For Development:
```python
# Use smaller datasets for testing
spark.conf.set("spark.sql.shuffle.partitions", "4")  # Reduce for small data
```

## Business Value

This Docker setup provides:
- **🔄 Consistent Environment**: Same environment across development and production
- **🚀 Fast Development**: VS Code integration with debugging capabilities
- **📊 Real-time Analytics**: Immediate access to processed data
- **🔧 Easy Maintenance**: Containerized dependencies and configurations
- **📈 Scalability**: Easy to scale up resources as data grows

## Next Steps

1. **Run the pipeline** with your data
2. **Explore the results** in each layer (bronze, silver, gold)
3. **Customize business rules** in the configuration files
4. **Set up monitoring** for production deployment
5. **Create dashboards** using the gold layer analytics

Your banking data pipeline is now ready to run in Docker with full VS Code development support!
