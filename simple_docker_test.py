"""
Simple Docker Test - No <PERSON> Lake Required
Business Purpose: Quick test to verify your Docker environment works
"""

import os
import sys
from pyspark.sql import SparkSession
from pyspark.sql.functions import *
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_basic_spark():
    """Test basic Spark functionality without Delta Lake"""
    logger.info("Testing basic Spark functionality...")
    
    try:
        # Create simple Spark session
        spark = SparkSession.builder \
            .appName("Simple Docker Test") \
            .getOrCreate()
        
        logger.info(f"✅ Spark initialized - Version: {spark.version}")
        
        # Test basic operations
        df = spark.range(10).withColumn("doubled", col("id") * 2)
        count = df.count()
        
        logger.info(f"✅ Basic Spark operations work - Count: {count}")
        
        # Show sample data
        logger.info("Sample data:")
        df.show(5)
        
        spark.stop()
        return True
        
    except Exception as e:
        logger.error(f"❌ Spark test failed: {e}")
        return False

def test_data_paths():
    """Test if your data paths exist"""
    logger.info("Testing data paths...")
    
    base_path = "/home/<USER>/Volumes/dlt_banking_project_catalog/dlt_project_schema/raw"
    
    paths_to_check = [
        f"{base_path}/2023/customers",
        f"{base_path}/2023/accounts", 
        f"{base_path}/2024/customers",
        f"{base_path}/2024/accounts"
    ]
    
    found_files = 0
    
    for path in paths_to_check:
        if os.path.exists(path):
            files = [f for f in os.listdir(path) if f.endswith('.csv')]
            logger.info(f"✅ Found {len(files)} CSV files in {path}")
            found_files += len(files)
            for file in files[:2]:  # Show first 2 files
                logger.info(f"   📄 {file}")
        else:
            logger.warning(f"⚠️  Path not found: {path}")
    
    if found_files > 0:
        logger.info(f"✅ Total CSV files found: {found_files}")
        return True
    else:
        logger.error("❌ No CSV files found")
        return False

def test_simple_data_loading():
    """Test loading your actual data files"""
    logger.info("Testing data loading...")
    
    try:
        spark = SparkSession.builder \
            .appName("Data Loading Test") \
            .getOrCreate()
        
        # Find a customer file to test
        base_path = "/home/<USER>/Volumes/dlt_banking_project_catalog/dlt_project_schema/raw"
        
        customer_file = None
        for year in ["2023", "2024"]:
            customer_path = f"{base_path}/{year}/customers"
            if os.path.exists(customer_path):
                files = [f for f in os.listdir(customer_path) if f.endswith('.csv')]
                if files:
                    customer_file = os.path.join(customer_path, files[0])
                    break
        
        if customer_file:
            logger.info(f"Testing with file: {customer_file}")
            
            # Load the file
            df = spark.read \
                .option("header", "true") \
                .option("inferSchema", "true") \
                .csv(customer_file)
            
            count = df.count()
            columns = len(df.columns)
            
            logger.info(f"✅ Loaded {count} records with {columns} columns")
            
            # Show schema
            logger.info("Schema:")
            df.printSchema()
            
            # Show sample data
            logger.info("Sample data:")
            df.show(3, truncate=False)
            
            spark.stop()
            return True
        else:
            logger.error("❌ No customer files found to test")
            spark.stop()
            return False
            
    except Exception as e:
        logger.error(f"❌ Data loading test failed: {e}")
        return False

def main():
    """Run simple tests"""
    logger.info("🐳 SIMPLE DOCKER ENVIRONMENT TEST")
    logger.info("=" * 50)
    
    tests = [
        ("Basic Spark", test_basic_spark),
        ("Data Paths", test_data_paths),
        ("Data Loading", test_simple_data_loading)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} Test ---")
        results[test_name] = test_func()
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 TEST RESULTS")
    logger.info("=" * 50)
    
    passed = len([r for r in results.values() if r])
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 Your Docker environment is working!")
        logger.info("Next steps:")
        logger.info("1. Install Delta Lake: python install_delta_lake.py")
        logger.info("2. Run full pipeline: python docker_pipeline_runner.py")
    else:
        logger.error("⚠️  Some tests failed. Check the issues above.")

if __name__ == "__main__":
    main()
