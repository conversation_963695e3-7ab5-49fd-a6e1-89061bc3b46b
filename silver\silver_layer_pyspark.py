"""
Silver Layer - PySpark Implementation
Business Purpose: Create analytics-ready datasets with enriched customer and transaction data
Alternative implementation for environments without Delta Live Tables
"""

from pyspark.sql import SparkSession, DataFrame
from pyspark.sql.functions import *
from pyspark.sql.types import *
from pyspark.sql.window import Window
from delta.tables import DeltaTable
import sys
import os

# Add utilities to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'utilities'))
from config import config
from data_quality import DataQualityUtils, BusinessRules

class SilverLayerProcessor:
    """
    Silver layer data processing using PySpark
    Business Purpose: Create analytics-ready datasets for business intelligence
    """
    
    def __init__(self, spark: SparkSession):
        self.spark = spark
        self.config = config
    
    def process_customer_enrichment(self, bronze_customer_path: str, output_path: str) -> None:
        """
        Business Context: Enrich customer data with analytics features
        Why: Enable customer segmentation and personalized banking services
        What: Add lifecycle metrics, segmentation, and digital adoption scores
        """
        
        print("Starting customer enrichment processing...")
        print("Business Purpose: Creating analytics-ready customer data for segmentation and insights")
        
        # Read bronze customer data
        customer_df = self.spark.read.format("delta").load(bronze_customer_path)
        
        print(f"Loaded {customer_df.count()} bronze customer records")
        
        # Apply enrichment transformations
        enriched_df = self._enrich_customer_data(customer_df)
        
        print(f"Enriched {enriched_df.count()} customer records with analytics features")
        
        # Write to Delta table
        self._write_delta_table(enriched_df, output_path, ["customer_id"], "silver_customer")
        
        print("Customer enrichment processing completed successfully")
    
    def process_transaction_enrichment(self, bronze_transaction_path: str, bronze_customer_path: str, output_path: str) -> None:
        """
        Business Context: Enrich transactions with customer context
        Why: Enable fraud detection and behavioral analytics
        What: Join transactions with customer data and add analytical features
        """
        
        print("Starting transaction enrichment processing...")
        print("Business Purpose: Creating enriched transaction data for fraud detection and analytics")
        
        # Read bronze data
        transactions_df = self.spark.read.format("delta").load(bronze_transaction_path)
        customer_df = self.spark.read.format("delta").load(bronze_customer_path)
        
        print(f"Loaded {transactions_df.count()} transaction records and {customer_df.count()} customer records")
        
        # Apply enrichment transformations
        enriched_df = self._enrich_transaction_data(transactions_df, customer_df)
        
        print(f"Enriched {enriched_df.count()} transaction records with customer context")
        
        # Write to Delta table
        self._write_delta_table(enriched_df, output_path, ["txn_id"], "silver_transaction")
        
        print("Transaction enrichment processing completed successfully")
    
    def process_account_summary(self, silver_transaction_path: str, output_path: str) -> None:
        """
        Business Context: Create account-level summaries for relationship banking
        Why: Enable account managers to understand customer relationships
        What: Aggregate transaction data into account-level metrics and KPIs
        """
        
        print("Starting account summary processing...")
        print("Business Purpose: Creating account-level insights for relationship management")
        
        # Read silver transaction data
        transactions_df = self.spark.read.format("delta").load(silver_transaction_path)
        
        print(f"Loaded {transactions_df.count()} enriched transaction records")
        
        # Create account summaries
        summary_df = self._create_account_summary(transactions_df)
        
        print(f"Created summaries for {summary_df.count()} accounts")
        
        # Write to Delta table
        self._write_delta_table(summary_df, output_path, ["account_id"], "silver_account_summary")
        
        print("Account summary processing completed successfully")
    
    def _enrich_customer_data(self, df: DataFrame) -> DataFrame:
        """
        Business Purpose: Add analytics features to customer data
        """
        
        print("Applying customer enrichment transformations...")
        
        # Customer lifecycle features
        df = df.withColumn("days_since_joining", 
            datediff(current_date(), to_date(col("join_date"))))
        
        df = df.withColumn("customer_tenure_category",
            when(col("days_since_joining") <= 90, "NEW")
            .when(col("days_since_joining") <= 365, "RECENT")
            .when(col("days_since_joining") <= 1095, "ESTABLISHED")
            .otherwise("LONG_TERM")
        )
        
        # Age-based segmentation
        df = df.withColumn("age_segment",
            when(col("customer_age") <= 25, "YOUNG_ADULT")
            .when(col("customer_age") <= 35, "MILLENNIAL")
            .when(col("customer_age") <= 50, "GEN_X")
            .when(col("customer_age") <= 65, "BABY_BOOMER")
            .otherwise("SENIOR")
        )
        
        # Income segmentation
        df = df.withColumn("income_segment_numeric",
            when(col("income_range") == "LOW", 1)
            .when(col("income_range") == "MEDIUM", 2)
            .when(col("income_range") == "HIGH", 3)
            .otherwise(0)
        )
        
        # Digital adoption score
        df = df.withColumn("digital_adoption_score",
            when(col("standardized_channel") == "MOBILE", 4)
            .when(col("standardized_channel") == "ONLINE", 3)
            .when(col("standardized_channel") == "ATM", 2)
            .when(col("standardized_channel") == "BRANCH", 1)
            .otherwise(0)
        )
        
        # Risk score calculation
        df = BusinessRules.calculate_risk_score(df)
        
        # Add processing metadata
        df = df.withColumn("silver_processed_timestamp", current_timestamp()) \
               .withColumn("silver_processed_date", current_date())
        
        return df
    
    def _enrich_transaction_data(self, transactions_df: DataFrame, customer_df: DataFrame) -> DataFrame:
        """
        Business Purpose: Enrich transactions with customer context and analytics features
        """
        
        print("Applying transaction enrichment transformations...")
        
        # Join with customer data
        enriched_df = transactions_df.alias("t").join(
            customer_df.alias("c"),
            col("t.customer_id") == col("c.customer_id"),
            "left"
        ).select(
            col("t.*"),
            col("c.name").alias("customer_name"),
            col("c.city").alias("customer_city"),
            col("c.standardized_status").alias("customer_status"),
            col("c.customer_age"),
            col("c.income_range"),
            col("c.risk_segment"),
            col("c.standardized_channel").alias("customer_preferred_channel")
        )
        
        # Transaction sequence analysis
        window_spec = Window.partitionBy("customer_id").orderBy("txn_date")
        
        enriched_df = enriched_df.withColumn("customer_txn_sequence", 
            row_number().over(window_spec))
        
        enriched_df = enriched_df.withColumn("days_since_last_txn",
            datediff(col("txn_date"), 
                    lag(col("txn_date"), 1).over(window_spec)))
        
        # Transaction frequency scoring
        enriched_df = enriched_df.withColumn("txn_frequency_score",
            when(col("days_since_last_txn") <= 1, 5)
            .when(col("days_since_last_txn") <= 7, 4)
            .when(col("days_since_last_txn") <= 30, 3)
            .when(col("days_since_last_txn") <= 90, 2)
            .otherwise(1)
        )
        
        # Channel consistency analysis
        enriched_df = enriched_df.withColumn("channel_consistency_flag",
            when(col("txn_channel") == col("customer_preferred_channel"), "CONSISTENT")
            .otherwise("DIFFERENT")
        )
        
        # Risk scoring
        enriched_df = enriched_df.withColumn("transaction_risk_score",
            when(col("is_suspicious_amount") == True, 3)
            .when(col("txn_category") == "VERY_LARGE", 2)
            .when(col("channel_consistency_flag") == "DIFFERENT", 1)
            .otherwise(0)
        )
        
        # Time-based features
        enriched_df = enriched_df.withColumn("is_weekend",
            when(col("txn_day_of_week").isin([1, 7]), True).otherwise(False))
        
        enriched_df = enriched_df.withColumn("time_of_day_category",
            when(col("txn_hour").between(6, 11), "MORNING")
            .when(col("txn_hour").between(12, 17), "AFTERNOON")
            .when(col("txn_hour").between(18, 22), "EVENING")
            .otherwise("NIGHT")
        )
        
        # Add processing metadata
        enriched_df = enriched_df.withColumn("silver_processed_timestamp", current_timestamp()) \
                               .withColumn("silver_processed_date", current_date())
        
        return enriched_df
    
    def _create_account_summary(self, df: DataFrame) -> DataFrame:
        """
        Business Purpose: Create account-level aggregations and metrics
        """
        
        print("Creating account-level summaries...")
        
        # Account-level aggregations
        summary_df = df.groupBy(
            "account_id", 
            "customer_id", 
            "customer_name",
            "account_type",
            "customer_city",
            "customer_status",
            "income_range",
            "risk_segment"
        ).agg(
            # Transaction metrics
            count("txn_id").alias("total_transactions"),
            countDistinct("txn_date").alias("active_days"),
            sum("standardized_txn_amount").alias("net_transaction_amount"),
            avg("standardized_txn_amount").alias("avg_transaction_amount"),
            
            # Balance metrics
            last("balance").alias("current_balance"),
            max("balance").alias("max_balance"),
            min("balance").alias("min_balance"),
            
            # Channel metrics
            countDistinct("txn_channel").alias("channels_used"),
            first("txn_channel").alias("most_used_channel"),
            
            # Risk metrics
            sum(when(col("is_suspicious_amount") == True, 1).otherwise(0)).alias("suspicious_transactions"),
            sum(when(col("transaction_risk_score") > 0, 1).otherwise(0)).alias("risky_transactions"),
            avg("transaction_risk_score").alias("avg_risk_score"),
            
            # Behavioral metrics
            sum(when(col("is_weekend") == True, 1).otherwise(0)).alias("weekend_transactions"),
            avg("txn_frequency_score").alias("avg_frequency_score"),
            
            # Date ranges
            min("txn_date").alias("first_transaction_date"),
            max("txn_date").alias("last_transaction_date")
        )
        
        # Calculate derived metrics
        summary_df = summary_df.withColumn("days_between_first_last_txn",
            datediff(col("last_transaction_date"), col("first_transaction_date")))
        
        summary_df = summary_df.withColumn("transaction_frequency",
            when(col("days_between_first_last_txn") > 0, 
                 col("total_transactions") / col("days_between_first_last_txn"))
            .otherwise(0))
        
        # Account health scoring
        summary_df = summary_df.withColumn("account_health_score",
            when(col("current_balance") > 0, 2).otherwise(0) +
            when(col("total_transactions") > 10, 2).otherwise(0) +
            when(col("avg_frequency_score") > 3, 1).otherwise(0) +
            when(col("suspicious_transactions") == 0, 1).otherwise(-1)
        )
        
        # Customer value segmentation
        summary_df = summary_df.withColumn("customer_value_segment",
            when((col("current_balance") > 10000) & (col("total_transactions") > 50), "HIGH_VALUE")
            .when((col("current_balance") > 5000) & (col("total_transactions") > 20), "MEDIUM_VALUE")
            .when(col("current_balance") > 0, "LOW_VALUE")
            .otherwise("NEGATIVE_VALUE")
        )
        
        # Add processing metadata
        summary_df = summary_df.withColumn("silver_processed_timestamp", current_timestamp()) \
                             .withColumn("silver_processed_date", current_date())
        
        return summary_df
    
    def _write_delta_table(self, df: DataFrame, output_path: str, merge_keys: list, table_name: str) -> None:
        """
        Business Purpose: Write data to Delta table with ACID properties
        """
        
        print(f"Writing {table_name} to Delta table at {output_path}")
        
        try:
            if DeltaTable.isDeltaTable(self.spark, output_path):
                print(f"Delta table exists, performing merge operation...")
                
                delta_table = DeltaTable.forPath(self.spark, output_path)
                merge_condition = " AND ".join([f"target.{key} = source.{key}" for key in merge_keys])
                
                delta_table.alias("target") \
                    .merge(df.alias("source"), merge_condition) \
                    .whenMatchedUpdateAll() \
                    .whenNotMatchedInsertAll() \
                    .execute()
                
                print(f"Merge completed for {table_name}")
            else:
                print(f"Creating new Delta table...")
                df.write \
                  .format("delta") \
                  .mode("overwrite") \
                  .option("mergeSchema", "true") \
                  .save(output_path)
                
                print(f"New Delta table created for {table_name}")
                
        except Exception as e:
            print(f"Error writing Delta table: {str(e)}")
            raise

# Example usage function
def run_silver_layer_processing(spark: SparkSession):
    """
    Business Purpose: Execute silver layer processing pipeline
    """
    
    processor = SilverLayerProcessor(spark)
    
    # Define paths (adjust based on your environment)
    bronze_customer_path = "/path/to/bronze/customer/data"
    bronze_transaction_path = "/path/to/bronze/transaction/data"
    
    silver_customer_path = "/path/to/silver/customer/data"
    silver_transaction_path = "/path/to/silver/transaction/data"
    silver_account_path = "/path/to/silver/account/data"
    
    # Process customer enrichment
    processor.process_customer_enrichment(bronze_customer_path, silver_customer_path)
    
    # Process transaction enrichment
    processor.process_transaction_enrichment(bronze_transaction_path, bronze_customer_path, silver_transaction_path)
    
    # Process account summaries
    processor.process_account_summary(silver_transaction_path, silver_account_path)
    
    print("Silver layer processing completed successfully!")
