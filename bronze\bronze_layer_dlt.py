"""
Bronze Layer - Delta Live Tables Implementation  
Business Purpose: Data cleansing, standardization, and quality enforcement
This layer transforms raw data into clean, validated datasets ready for analytics
"""

import dlt
from pyspark.sql.functions import *
from pyspark.sql.types import *
import sys
import os

# Add utilities to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'utilities'))
from config import config
from data_quality import DataQualityUtils, BusinessRules

# ============================================================================
# BRONZE CUSTOMER DATA - CLEANED AND VALIDATED
# ============================================================================

@dlt.table(
    name=config.BRONZE_CUSTOMER_TABLE,
    comment="""
    Bronze layer customer data with data quality rules and standardization.
    Business Purpose: Clean customer data for KYC compliance, risk assessment,
    and customer analytics while maintaining audit trail.
    """,
    table_properties={
        "quality": "bronze",
        "pipelines.autoOptimize.managed": "true"
    }
)
@dlt.expect_or_fail("valid_customer_id", "customer_id IS NOT NULL")
@dlt.expect_or_drop("valid_customer_name", "name IS NOT NULL AND trim(name) != ''")
@dlt.expect_or_drop("valid_customer_dob", "dob IS NOT NULL AND dob != ''")
@dlt.expect_or_drop("valid_customer_city", "city IS NOT NULL AND trim(city) != ''")
@dlt.expect_or_drop("valid_customer_join_date", "join_date IS NOT NULL AND join_date <= current_date()")
@dlt.expect_or_drop("valid_customer_email", "email IS NOT NULL AND email RLIKE '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}$'")
@dlt.expect_or_drop("valid_customer_phone_number", "cleaned_phone_number IS NOT NULL AND length(cleaned_phone_number) >= 10")
@dlt.expect_or_drop("valid_customer_channel", "standardized_channel IS NOT NULL")
@dlt.expect("valid_gender", "standardized_gender IN ('MALE', 'FEMALE', 'UNKNOWN')")
@dlt.expect("valid_status", "standardized_status IS NOT NULL")
def bronze_customer_cleaned():
    """
    Business Context: Clean and standardize customer data for downstream analytics
    Why: Ensure data quality for regulatory compliance and accurate customer insights
    What: Standardized customer demographics with validated contact information
    """
    
    df = dlt.read_stream(config.LANDING_CUSTOMER_TABLE)
    
    # Data Standardization - Business Purpose: Ensure consistent data formats
    df = df.select(
        col("customer_id"),
        DataQualityUtils.standardize_text_fields("name").alias("name"),
        col("dob"),
        DataQualityUtils.standardize_gender("gender").alias("standardized_gender"),
        DataQualityUtils.standardize_text_fields("city").alias("city"),
        col("join_date"),
        DataQualityUtils.standardize_status("status").alias("standardized_status"),
        lower(trim(col("email"))).alias("email"),
        DataQualityUtils.clean_phone_number("phone_number").alias("cleaned_phone_number"),
        DataQualityUtils.standardize_text_fields("preferred_channel").alias("standardized_channel"),
        DataQualityUtils.standardize_text_fields("occupation").alias("occupation"),
        DataQualityUtils.standardize_text_fields("income_range").alias("income_range"),
        DataQualityUtils.standardize_text_fields("risk_segment").alias("risk_segment"),
        col("_source_file"),
        col("_ingestion_timestamp"),
        col("_ingestion_date")
    )
    
    # Business Rule: Calculate customer age for demographic analysis
    df = df.withColumn("customer_age", DataQualityUtils.calculate_age("dob"))
    
    # Business Rule: Validate phone numbers for customer communication
    df = df.filter(DataQualityUtils.validate_phone_number("cleaned_phone_number"))
    
    # Business Rule: Filter valid channels for customer preferences
    df = df.filter(col("standardized_channel").isin(config.VALID_CHANNELS))
    
    # Business Rule: Ensure join dates are not in the future
    df = df.filter(col("join_date") <= current_date())
    
    # Add data quality metadata
    df = DataQualityUtils.add_data_quality_flags(df, "bronze_customer")
    
    return df

# ============================================================================
# BRONZE TRANSACTIONS DATA - CLEANED AND VALIDATED
# ============================================================================

@dlt.table(
    name=config.BRONZE_TRANSACTIONS_TABLE,
    comment="""
    Bronze layer transactions data with business rules and fraud detection flags.
    Business Purpose: Clean transaction data for fraud detection, compliance
    reporting, and customer behavior analysis.
    """,
    table_properties={
        "quality": "bronze",
        "pipelines.autoOptimize.managed": "true"
    }
)
@dlt.expect_or_fail("valid_account_id", "account_id IS NOT NULL")
@dlt.expect_or_fail("valid_customer_id", "customer_id IS NOT NULL") 
@dlt.expect_or_fail("valid_txn_id", "txn_id IS NOT NULL")
@dlt.expect_or_drop("valid_account_type", "account_type IS NOT NULL AND account_type IN ('Current', 'Savings', 'Credit', 'Investment')")
@dlt.expect_or_drop("valid_balance", "balance IS NOT NULL")
@dlt.expect_or_drop("valid_txn_date", "txn_date IS NOT NULL AND txn_date <= current_date()")
@dlt.expect_or_drop("valid_txn_type", "txn_type IS NOT NULL AND txn_type IN ('Credit', 'Debit')")
@dlt.expect_or_drop("valid_txn_amount", "txn_amount IS NOT NULL AND abs(txn_amount) >= 0.01")
@dlt.expect_or_drop("valid_txn_channel", "txn_channel IS NOT NULL AND txn_channel IN ('Online', 'Mobile', 'Branch', 'ATM')")
def bronze_accounts_transactions_cleaned():
    """
    Business Context: Clean and validate transaction data for fraud detection
    Why: Ensure transaction integrity for compliance and risk management
    What: Validated transactions with fraud detection flags and business rules
    """
    
    df = dlt.read_stream(config.LANDING_TRANSACTIONS_TABLE)
    
    # Data Standardization - Business Purpose: Ensure consistent transaction formats
    df = df.select(
        col("account_id"),
        col("customer_id"),
        DataQualityUtils.standardize_text_fields("account_type").alias("account_type"),
        col("balance"),
        col("txn_id"),
        to_date(col("txn_date")).alias("txn_date"),
        DataQualityUtils.standardize_text_fields("txn_type").alias("txn_type"),
        col("txn_amount"),
        DataQualityUtils.standardize_text_fields("txn_channel").alias("txn_channel"),
        col("_source_file"),
        col("_ingestion_timestamp"),
        col("_ingestion_date")
    )
    
    # Business Rule: Standardize transaction amounts based on type
    # Debits should be negative, Credits should be positive
    df = df.withColumn("standardized_txn_amount",
        when(col("txn_type") == "DEBIT", -abs(col("txn_amount")))
        .when(col("txn_type") == "CREDIT", abs(col("txn_amount")))
        .otherwise(col("txn_amount"))
    )
    
    # Business Rule: Add transaction categorization
    df = df.withColumn("txn_category",
        when(abs(col("txn_amount")) <= 50, "SMALL")
        .when(abs(col("txn_amount")) <= 500, "MEDIUM") 
        .when(abs(col("txn_amount")) <= 5000, "LARGE")
        .otherwise("VERY_LARGE")
    )
    
    # Business Rule: Flag suspicious transactions for fraud detection
    df = df.withColumn("is_suspicious_amount", 
        DataQualityUtils.detect_suspicious_transactions("txn_amount"))
    
    # Business Rule: Add time-based features for analytics
    df = df.withColumn("txn_year", year(col("txn_date"))) \
           .withColumn("txn_month", month(col("txn_date"))) \
           .withColumn("txn_day_of_week", dayofweek(col("txn_date"))) \
           .withColumn("txn_hour", hour(col("_ingestion_timestamp")))
    
    # Business Rule: Validate transaction amounts are within business limits
    df = df.filter(DataQualityUtils.validate_transaction_amount("txn_amount"))
    
    # Business Rule: Filter valid account types and channels
    df = df.filter(col("account_type").isin(config.VALID_ACCOUNT_TYPES))
    df = df.filter(col("txn_channel").isin(config.VALID_CHANNELS))
    
    # Business Rule: Ensure transaction dates are not in the future
    df = df.filter(col("txn_date") <= current_date())
    
    # Add data quality metadata
    df = DataQualityUtils.add_data_quality_flags(df, "bronze_transactions")
    
    return df

# ============================================================================
# BRONZE DATA QUALITY SUMMARY
# ============================================================================

@dlt.table(
    name="bronze_data_quality_summary",
    comment="""
    Summary of data quality metrics for bronze layer monitoring.
    Business Purpose: Track data quality KPIs for operational excellence
    and regulatory compliance reporting.
    """
)
def bronze_data_quality_summary():
    """
    Business Context: Monitor bronze layer data quality for compliance
    Why: Maintain high data quality standards for risk management
    What: Quality metrics, validation results, and trend analysis
    """
    
    customer_df = dlt.read(config.BRONZE_CUSTOMER_TABLE)
    transactions_df = dlt.read(config.BRONZE_TRANSACTIONS_TABLE)
    
    # Customer quality summary
    customer_summary = customer_df.agg(
        lit("customers").alias("table_name"),
        count("*").alias("total_records"),
        countDistinct("customer_id").alias("unique_customers"),
        sum(when(col("customer_age") < 18, 1).otherwise(0)).alias("underage_customers"),
        sum(when(col("standardized_status") == "ACTIVE", 1).otherwise(0)).alias("active_customers"),
        avg("customer_age").alias("avg_customer_age")
    )
    
    # Transaction quality summary
    transaction_summary = transactions_df.agg(
        lit("transactions").alias("table_name"),
        count("*").alias("total_records"),
        countDistinct("customer_id").alias("unique_customers"),
        sum(when(col("is_suspicious_amount") == True, 1).otherwise(0)).alias("suspicious_transactions"),
        sum("standardized_txn_amount").alias("total_transaction_value"),
        avg("standardized_txn_amount").alias("avg_transaction_amount")
    )
    
    return customer_summary.unionByName(transaction_summary, allowMissingColumns=True) \
                          .withColumn("quality_check_timestamp", current_timestamp()) \
                          .withColumn("quality_check_date", current_date())
