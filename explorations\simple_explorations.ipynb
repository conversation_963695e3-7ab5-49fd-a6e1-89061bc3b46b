{"cells": [{"cell_type": "markdown", "id": "c54e68d6", "metadata": {}, "source": ["## **Example Exploratory Notebook**\n", "\n", " - This notebook is used to explore the data in the Delta Lake table genrated by pipeline using your prefered programming language.\n", " - NOTE : This is not execute as a part of pipleline."]}, {"cell_type": "code", "execution_count": null, "id": "f12b1089", "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append('/Workspace/Users/<USER>/DLT_BANKING_PROJECT')\n"]}, {"cell_type": "code", "execution_count": null, "id": "8997ce0a", "metadata": {}, "outputs": [], "source": ["# !!! Before perfroming any analysis, make sure to run the pipeline to materialize the sample datasets. The tables referenced in this notebook are delta tables genrated by the pipeline deonds on that step.\n", "\n", "display(spark.sql('SELECT * FROM dlt_banking_project_caltalog.dlt_bank_project_schema.sample_trips_dlt_banking_project_pipeline'))"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}