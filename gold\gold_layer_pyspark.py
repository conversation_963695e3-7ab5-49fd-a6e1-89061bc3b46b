"""
Gold Layer - PySpark Implementation
Business Purpose: Create business-specific aggregations, KPIs, and analytics marts
Alternative implementation for environments without Delta Live Tables
"""

from pyspark.sql import SparkSession, DataFrame
from pyspark.sql.functions import *
from pyspark.sql.types import *
from pyspark.sql.window import Window
from delta.tables import DeltaTable
import sys
import os

# Add utilities to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'utilities'))
from config import config
from data_quality import DataQualityUtils, BusinessRules

class GoldLayerProcessor:
    """
    Gold layer data processing using PySpark
    Business Purpose: Create business intelligence and executive reporting datasets
    """
    
    def __init__(self, spark: SparkSession):
        self.spark = spark
        self.config = config
    
    def process_customer_analytics(self, silver_customer_path: str, silver_account_path: str, output_path: str) -> None:
        """
        Business Context: Create customer analytics for strategic business decisions
        Why: Enable data-driven customer strategy and product development
        What: Customer lifetime value, engagement scores, and churn risk analysis
        """
        
        print("Starting customer analytics processing...")
        print("Business Purpose: Creating customer insights for strategic decision making")
        
        # Read silver layer data
        customer_df = self.spark.read.format("delta").load(silver_customer_path)
        account_df = self.spark.read.format("delta").load(silver_account_path)
        
        print(f"Loaded {customer_df.count()} customers and {account_df.count()} accounts")
        
        # Create customer analytics
        analytics_df = self._create_customer_analytics(customer_df, account_df)
        
        print(f"Generated analytics for {analytics_df.count()} customers")
        
        # Write to Delta table
        self._write_delta_table(analytics_df, output_path, ["customer_id"], "gold_customer_analytics")
        
        print("Customer analytics processing completed successfully")
    
    def process_transaction_summary(self, silver_transaction_path: str, output_path: str) -> None:
        """
        Business Context: Create transaction summaries for business performance monitoring
        Why: Enable operational excellence and revenue optimization
        What: Daily transaction volumes, channel performance, and operational KPIs
        """
        
        print("Starting transaction summary processing...")
        print("Business Purpose: Creating transaction insights for operational excellence")
        
        # Read silver transaction data
        transactions_df = self.spark.read.format("delta").load(silver_transaction_path)
        
        print(f"Loaded {transactions_df.count()} transactions")
        
        # Create transaction summaries
        summary_df = self._create_transaction_summary(transactions_df)
        
        print(f"Generated {summary_df.count()} daily transaction summaries")
        
        # Write to Delta table
        self._write_delta_table(summary_df, output_path, ["txn_date", "txn_channel"], "gold_transaction_summary")
        
        print("Transaction summary processing completed successfully")
    
    def process_risk_metrics(self, silver_customer_path: str, silver_transaction_path: str, output_path: str) -> None:
        """
        Business Context: Create risk metrics for compliance and risk management
        Why: Ensure regulatory compliance and minimize operational risk
        What: Risk scores, compliance metrics, and fraud indicators
        """
        
        print("Starting risk metrics processing...")
        print("Business Purpose: Creating risk insights for compliance and risk management")
        
        # Read silver layer data
        customer_df = self.spark.read.format("delta").load(silver_customer_path)
        transactions_df = self.spark.read.format("delta").load(silver_transaction_path)
        
        print(f"Loaded {customer_df.count()} customers and {transactions_df.count()} transactions")
        
        # Create risk metrics
        risk_df = self._create_risk_metrics(customer_df, transactions_df)
        
        print(f"Generated {risk_df.count()} risk metric records")
        
        # Write to Delta table
        self._write_delta_table(risk_df, output_path, ["risk_assessment_date"], "gold_risk_metrics")
        
        print("Risk metrics processing completed successfully")
    
    def process_monthly_kpi(self, silver_customer_path: str, silver_transaction_path: str, 
                          silver_account_path: str, output_path: str) -> None:
        """
        Business Context: Create monthly KPIs for executive reporting
        Why: Enable strategic decision making with high-level business metrics
        What: Revenue, growth, customer acquisition, and operational KPIs
        """
        
        print("Starting monthly KPI processing...")
        print("Business Purpose: Creating executive KPIs for strategic decision making")
        
        # Read silver layer data
        customer_df = self.spark.read.format("delta").load(silver_customer_path)
        transactions_df = self.spark.read.format("delta").load(silver_transaction_path)
        accounts_df = self.spark.read.format("delta").load(silver_account_path)
        
        print(f"Loaded data for KPI calculation")
        
        # Create monthly KPIs
        kpi_df = self._create_monthly_kpi(customer_df, transactions_df, accounts_df)
        
        print(f"Generated {kpi_df.count()} monthly KPI records")
        
        # Write to Delta table
        self._write_delta_table(kpi_df, output_path, ["kpi_month"], "gold_monthly_kpi")
        
        print("Monthly KPI processing completed successfully")
    
    def _create_customer_analytics(self, customer_df: DataFrame, account_df: DataFrame) -> DataFrame:
        """
        Business Purpose: Create comprehensive customer analytics
        """
        
        print("Creating customer analytics...")
        
        # Join customer with account summaries
        analytics_df = customer_df.alias("c").join(
            account_df.alias("a"),
            col("c.customer_id") == col("a.customer_id"),
            "left"
        )
        
        # Customer Lifetime Value estimation
        analytics_df = analytics_df.withColumn("estimated_clv",
            coalesce(col("net_transaction_amount"), lit(0)) * 
            (col("days_since_joining") / 365.0) * 
            col("digital_adoption_score") * 
            when(col("customer_value_segment") == "HIGH_VALUE", 2.0)
            .when(col("customer_value_segment") == "MEDIUM_VALUE", 1.5)
            .otherwise(1.0)
        )
        
        # Customer Engagement Score
        analytics_df = analytics_df.withColumn("engagement_score",
            (coalesce(col("total_transactions"), lit(0)) / greatest(col("days_since_joining"), lit(1))) * 365 +
            col("digital_adoption_score") * 10 +
            coalesce(col("channels_used"), lit(1)) * 5 +
            when(col("standardized_status") == "ACTIVE", 20).otherwise(0)
        )
        
        # Churn Risk Score
        analytics_df = analytics_df.withColumn("churn_risk_score",
            when(col("days_since_joining") < 90, 3)
            .when(coalesce(col("total_transactions"), lit(0)) < 5, 4)
            .when(coalesce(col("current_balance"), lit(0)) < 0, 5)
            .when(col("standardized_status") != "ACTIVE", 5)
            .otherwise(1)
        )
        
        # Product Affinity Scores
        analytics_df = analytics_df.withColumn("savings_affinity",
            when(col("account_type") == "SAVINGS", 5)
            .when(col("income_segment_numeric") >= 2, 3)
            .when(col("age_segment").isin(["GEN_X", "BABY_BOOMER"]), 4)
            .otherwise(2)
        )
        
        analytics_df = analytics_df.withColumn("credit_affinity",
            when(col("account_type") == "CREDIT", 5)
            .when((col("income_segment_numeric") >= 2) & (col("calculated_risk_score") <= 2), 4)
            .when(col("age_segment").isin(["MILLENNIAL", "GEN_X"]), 3)
            .otherwise(1)
        )
        
        # Digital Readiness Score
        analytics_df = analytics_df.withColumn("digital_readiness_score",
            col("digital_adoption_score") * 2 +
            when(col("age_segment").isin(["YOUNG_ADULT", "MILLENNIAL"]), 3).otherwise(1) +
            when(coalesce(col("channels_used"), lit(1)) > 2, 2).otherwise(0)
        )
        
        # Select final columns
        final_df = analytics_df.select(
            col("c.customer_id"),
            col("c.name"),
            col("c.age_segment"),
            col("c.customer_tenure_category"),
            col("c.income_range"),
            col("c.digital_adoption_score"),
            coalesce(col("a.customer_value_segment"), lit("UNKNOWN")).alias("customer_value_segment"),
            coalesce(col("a.total_transactions"), lit(0)).alias("total_transactions"),
            coalesce(col("a.current_balance"), lit(0)).alias("current_balance"),
            col("estimated_clv"),
            col("engagement_score"),
            col("churn_risk_score"),
            col("savings_affinity"),
            col("credit_affinity"),
            col("digital_readiness_score"),
            current_timestamp().alias("analytics_generated_timestamp"),
            current_date().alias("analytics_generated_date")
        )
        
        return final_df
    
    def _create_transaction_summary(self, transactions_df: DataFrame) -> DataFrame:
        """
        Business Purpose: Create daily transaction summaries for operational monitoring
        """
        
        print("Creating transaction summaries...")
        
        # Daily transaction summaries
        summary_df = transactions_df.groupBy(
            col("txn_date"),
            col("txn_type"),
            col("txn_channel"),
            col("account_type")
        ).agg(
            count("txn_id").alias("transaction_count"),
            sum("standardized_txn_amount").alias("total_amount"),
            avg("standardized_txn_amount").alias("avg_amount"),
            countDistinct("customer_id").alias("unique_customers"),
            countDistinct("account_id").alias("unique_accounts"),
            sum(when(col("is_suspicious_amount") == True, 1).otherwise(0)).alias("suspicious_count"),
            sum(when(col("transaction_risk_score") > 2, 1).otherwise(0)).alias("high_risk_count")
        )
        
        # Channel Performance Score
        summary_df = summary_df.withColumn("channel_performance_score",
            (col("transaction_count") / 100.0) +
            (col("total_amount") / 10000.0) +
            when(col("suspicious_count") == 0, 10).otherwise(0) -
            (col("high_risk_count") * 2)
        )
        
        # Operational Efficiency Score
        summary_df = summary_df.withColumn("operational_efficiency_score",
            col("transaction_count") / greatest(col("unique_customers"), lit(1)) +
            when(col("txn_channel").isin(["MOBILE", "ONLINE"]), 2).otherwise(1)
        )
        
        # Add metadata
        final_summary = summary_df.withColumn("summary_generated_timestamp", current_timestamp()) \
                                 .withColumn("summary_generated_date", current_date())
        
        return final_summary
    
    def _create_risk_metrics(self, customer_df: DataFrame, transactions_df: DataFrame) -> DataFrame:
        """
        Business Purpose: Create risk metrics for compliance monitoring
        """
        
        print("Creating risk metrics...")
        
        # Customer risk aggregations
        customer_risk = customer_df.groupBy(
            col("risk_segment"),
            col("age_segment"),
            col("income_range"),
            col("customer_tenure_category")
        ).agg(
            count("customer_id").alias("customer_count"),
            avg("calculated_risk_score").alias("avg_risk_score"),
            sum(when(col("standardized_status") == "ACTIVE", 1).otherwise(0)).alias("active_customers"),
            avg("digital_adoption_score").alias("avg_digital_adoption")
        )
        
        # Transaction risk aggregations
        transaction_risk = transactions_df.groupBy(
            date_trunc("month", col("txn_date")).alias("risk_month"),
            col("txn_channel"),
            col("customer_city")
        ).agg(
            count("txn_id").alias("total_transactions"),
            sum(when(col("is_suspicious_amount") == True, 1).otherwise(0)).alias("suspicious_transactions"),
            sum(when(col("transaction_risk_score") > 2, 1).otherwise(0)).alias("high_risk_transactions"),
            avg("transaction_risk_score").alias("avg_transaction_risk"),
            sum("standardized_txn_amount").alias("total_transaction_value")
        )
        
        # Risk Concentration Score
        transaction_risk = transaction_risk.withColumn("risk_concentration_score",
            (col("suspicious_transactions") / greatest(col("total_transactions"), lit(1))) * 100 +
            (col("high_risk_transactions") / greatest(col("total_transactions"), lit(1))) * 50
        )
        
        # Compliance Score
        transaction_risk = transaction_risk.withColumn("compliance_score",
            when(col("risk_concentration_score") <= 5, 5)
            .when(col("risk_concentration_score") <= 10, 4)
            .when(col("risk_concentration_score") <= 20, 3)
            .when(col("risk_concentration_score") <= 30, 2)
            .otherwise(1)
        )
        
        # Combine risk metrics
        combined_risk = customer_risk.crossJoin(transaction_risk) \
                                   .withColumn("risk_assessment_timestamp", current_timestamp()) \
                                   .withColumn("risk_assessment_date", current_date())
        
        return combined_risk
    
    def _create_monthly_kpi(self, customer_df: DataFrame, transactions_df: DataFrame, accounts_df: DataFrame) -> DataFrame:
        """
        Business Purpose: Create monthly KPIs for executive dashboard
        """
        
        print("Creating monthly KPIs...")
        
        # Monthly customer metrics
        monthly_customers = customer_df.withColumn("join_month", 
            date_trunc("month", to_date(col("join_date")))) \
            .groupBy("join_month") \
            .agg(
                count("customer_id").alias("new_customers"),
                countDistinct("city").alias("cities_served"),
                avg("customer_age").alias("avg_customer_age"),
                sum(when(col("digital_adoption_score") >= 3, 1).otherwise(0)).alias("digital_customers")
            )
        
        # Monthly transaction metrics
        monthly_transactions = transactions_df.withColumn("txn_month",
            date_trunc("month", col("txn_date"))) \
            .groupBy("txn_month") \
            .agg(
                count("txn_id").alias("total_transactions"),
                sum("standardized_txn_amount").alias("total_revenue"),
                countDistinct("customer_id").alias("active_customers"),
                avg("standardized_txn_amount").alias("avg_transaction_value"),
                sum(when(col("txn_channel").isin(["MOBILE", "ONLINE"]), 1).otherwise(0)).alias("digital_transactions")
            )
        
        # Monthly account metrics
        monthly_accounts = accounts_df.groupBy(
            date_trunc("month", col("silver_processed_date")).alias("account_month")
        ).agg(
            count("account_id").alias("total_accounts"),
            sum("current_balance").alias("total_deposits"),
            sum(when(col("customer_value_segment") == "HIGH_VALUE", 1).otherwise(0)).alias("high_value_accounts"),
            avg("account_health_score").alias("avg_account_health")
        )
        
        # Join all monthly metrics
        monthly_kpi = monthly_customers.alias("c") \
            .join(monthly_transactions.alias("t"), 
                  col("c.join_month") == col("t.txn_month"), "full_outer") \
            .join(monthly_accounts.alias("a"),
                  coalesce(col("c.join_month"), col("t.txn_month")) == col("a.account_month"), "full_outer") \
            .select(
                coalesce(col("c.join_month"), col("t.txn_month"), col("a.account_month")).alias("kpi_month"),
                coalesce(col("new_customers"), lit(0)).alias("new_customers"),
                coalesce(col("total_transactions"), lit(0)).alias("total_transactions"),
                coalesce(col("total_revenue"), lit(0)).alias("total_revenue"),
                coalesce(col("active_customers"), lit(0)).alias("active_customers"),
                coalesce(col("total_accounts"), lit(0)).alias("total_accounts"),
                coalesce(col("total_deposits"), lit(0)).alias("total_deposits"),
                coalesce(col("high_value_accounts"), lit(0)).alias("high_value_accounts"),
                coalesce(col("digital_customers"), lit(0)).alias("digital_customers"),
                coalesce(col("digital_transactions"), lit(0)).alias("digital_transactions"),
                coalesce(col("avg_customer_age"), lit(0)).alias("avg_customer_age"),
                coalesce(col("avg_transaction_value"), lit(0)).alias("avg_transaction_value"),
                coalesce(col("avg_account_health"), lit(0)).alias("avg_account_health")
            )
        
        # Calculate business KPIs
        monthly_kpi = monthly_kpi.withColumn("digital_adoption_rate",
            col("digital_transactions") / greatest(col("total_transactions"), lit(1)) * 100)
        
        monthly_kpi = monthly_kpi.withColumn("customer_acquisition_cost",
            col("total_revenue") / greatest(col("new_customers"), lit(1)))
        
        monthly_kpi = monthly_kpi.withColumn("revenue_per_customer",
            col("total_revenue") / greatest(col("active_customers"), lit(1)))
        
        # Add metadata
        final_kpi = monthly_kpi.withColumn("kpi_generated_timestamp", current_timestamp()) \
                              .withColumn("kpi_generated_date", current_date())
        
        return final_kpi
    
    def _write_delta_table(self, df: DataFrame, output_path: str, merge_keys: list, table_name: str) -> None:
        """
        Business Purpose: Write data to Delta table with ACID properties
        """
        
        print(f"Writing {table_name} to Delta table at {output_path}")
        
        try:
            if DeltaTable.isDeltaTable(self.spark, output_path):
                print(f"Delta table exists, performing merge operation...")
                
                delta_table = DeltaTable.forPath(self.spark, output_path)
                merge_condition = " AND ".join([f"target.{key} = source.{key}" for key in merge_keys])
                
                delta_table.alias("target") \
                    .merge(df.alias("source"), merge_condition) \
                    .whenMatchedUpdateAll() \
                    .whenNotMatchedInsertAll() \
                    .execute()
                
                print(f"Merge completed for {table_name}")
            else:
                print(f"Creating new Delta table...")
                df.write \
                  .format("delta") \
                  .mode("overwrite") \
                  .option("mergeSchema", "true") \
                  .save(output_path)
                
                print(f"New Delta table created for {table_name}")
                
        except Exception as e:
            print(f"Error writing Delta table: {str(e)}")
            raise

# Example usage function
def run_gold_layer_processing(spark: SparkSession):
    """
    Business Purpose: Execute gold layer processing pipeline
    """
    
    processor = GoldLayerProcessor(spark)
    
    # Define paths (adjust based on your environment)
    silver_customer_path = "/path/to/silver/customer/data"
    silver_transaction_path = "/path/to/silver/transaction/data"
    silver_account_path = "/path/to/silver/account/data"
    
    gold_customer_analytics_path = "/path/to/gold/customer_analytics/data"
    gold_transaction_summary_path = "/path/to/gold/transaction_summary/data"
    gold_risk_metrics_path = "/path/to/gold/risk_metrics/data"
    gold_monthly_kpi_path = "/path/to/gold/monthly_kpi/data"
    
    # Process all gold layer tables
    processor.process_customer_analytics(silver_customer_path, silver_account_path, gold_customer_analytics_path)
    processor.process_transaction_summary(silver_transaction_path, gold_transaction_summary_path)
    processor.process_risk_metrics(silver_customer_path, silver_transaction_path, gold_risk_metrics_path)
    processor.process_monthly_kpi(silver_customer_path, silver_transaction_path, silver_account_path, gold_monthly_kpi_path)
    
    print("Gold layer processing completed successfully!")
