"""
Docker Setup Test Script
Business Purpose: Verify Docker environment and data accessibility
Test script to ensure everything is configured correctly before running the full pipeline
"""

import os
import sys
from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
import logging

# Add utilities to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'utilities'))
from config import config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_spark_initialization():
    """
    Business Purpose: Test Spark session creation with Delta Lake support
    """
    logger.info("Testing Spark initialization...")
    
    try:
        spark = SparkSession.builder \
            .appName("Docker Setup Test") \
            .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
            .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
            .getOrCreate()
        
        logger.info(f"✅ Spark initialized successfully - Version: {spark.version}")
        logger.info(f"✅ Spark UI available at: {spark.sparkContext.uiWebUrl}")
        
        # Test basic Spark functionality
        test_df = spark.range(10).withColumn("test_col", col("id") * 2)
        count = test_df.count()
        logger.info(f"✅ Basic Spark operations working - Test DataFrame count: {count}")
        
        return spark
        
    except Exception as e:
        logger.error(f"❌ Spark initialization failed: {str(e)}")
        return None

def test_data_file_accessibility():
    """
    Business Purpose: Verify that data files are accessible in the expected locations
    """
    logger.info("Testing data file accessibility...")
    
    # Test base path
    base_path = config.DOCKER_BASE_PATH
    logger.info(f"Testing base path: {base_path}")
    
    if os.path.exists(base_path):
        logger.info(f"✅ Base path exists: {base_path}")
    else:
        logger.error(f"❌ Base path not found: {base_path}")
        return False
    
    # Test data paths
    data_paths = [
        config.RAW_CUSTOMER_2023_PATH,
        config.RAW_CUSTOMER_2024_PATH,
        config.RAW_ACCOUNTS_2023_PATH,
        config.RAW_ACCOUNTS_2024_PATH
    ]
    
    found_files = []
    
    for path in data_paths:
        logger.info(f"Checking path: {path}")
        
        if os.path.exists(path):
            files = [f for f in os.listdir(path) if f.endswith('.csv')]
            if files:
                logger.info(f"✅ Found {len(files)} CSV files in {path}")
                for file in files:
                    full_path = os.path.join(path, file)
                    found_files.append(full_path)
                    logger.info(f"   📄 {file}")
            else:
                logger.warning(f"⚠️  Path exists but no CSV files found: {path}")
        else:
            logger.warning(f"⚠️  Path not found: {path}")
    
    if found_files:
        logger.info(f"✅ Total data files found: {len(found_files)}")
        return found_files
    else:
        logger.error("❌ No data files found in any location")
        return []

def test_data_loading(spark, file_paths):
    """
    Business Purpose: Test loading and basic processing of your data files
    """
    logger.info("Testing data loading and basic processing...")
    
    if not file_paths:
        logger.error("❌ No files to test")
        return False
    
    # Test loading customer files
    customer_files = [f for f in file_paths if 'customer' in f.lower()]
    transaction_files = [f for f in file_paths if 'account' in f.lower() or 'transaction' in f.lower()]
    
    logger.info(f"Customer files to test: {len(customer_files)}")
    logger.info(f"Transaction files to test: {len(transaction_files)}")
    
    # Test customer data loading
    if customer_files:
        logger.info("Testing customer data loading...")
        try:
            customer_schema = config.get_customer_schema()
            
            for file_path in customer_files[:2]:  # Test first 2 files
                logger.info(f"Loading: {file_path}")
                
                df = spark.read \
                    .option("header", "true") \
                    .option("multiline", "true") \
                    .schema(customer_schema) \
                    .csv(file_path)
                
                count = df.count()
                columns = len(df.columns)
                
                logger.info(f"✅ Loaded {count} customer records with {columns} columns")
                
                # Show sample data
                logger.info("Sample customer data:")
                df.select("customer_id", "name", "city", "email").show(3, truncate=False)
                
                # Basic data quality check
                null_ids = df.filter(col("customer_id").isNull()).count()
                logger.info(f"Data quality check - Null customer IDs: {null_ids}")
                
        except Exception as e:
            logger.error(f"❌ Customer data loading failed: {str(e)}")
            return False
    
    # Test transaction data loading
    if transaction_files:
        logger.info("Testing transaction data loading...")
        try:
            transaction_schema = config.get_transactions_schema()
            
            for file_path in transaction_files[:2]:  # Test first 2 files
                logger.info(f"Loading: {file_path}")
                
                df = spark.read \
                    .option("header", "true") \
                    .option("multiline", "true") \
                    .schema(transaction_schema) \
                    .csv(file_path)
                
                count = df.count()
                columns = len(df.columns)
                
                logger.info(f"✅ Loaded {count} transaction records with {columns} columns")
                
                # Show sample data
                logger.info("Sample transaction data:")
                df.select("account_id", "customer_id", "txn_type", "txn_amount", "txn_channel").show(3, truncate=False)
                
                # Basic data quality check
                null_txn_ids = df.filter(col("txn_id").isNull()).count()
                logger.info(f"Data quality check - Null transaction IDs: {null_txn_ids}")
                
        except Exception as e:
            logger.error(f"❌ Transaction data loading failed: {str(e)}")
            return False
    
    logger.info("✅ Data loading tests completed successfully")
    return True

def test_directory_creation():
    """
    Business Purpose: Test creation of output directories for pipeline
    """
    logger.info("Testing directory creation for pipeline outputs...")
    
    directories = [
        config.CHECKPOINT_PATH,
        config.BRONZE_PATH,
        config.SILVER_PATH,
        config.GOLD_PATH
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            if os.path.exists(directory):
                logger.info(f"✅ Directory ready: {directory}")
            else:
                logger.error(f"❌ Failed to create directory: {directory}")
                return False
        except Exception as e:
            logger.error(f"❌ Error creating directory {directory}: {str(e)}")
            return False
    
    logger.info("✅ All directories created successfully")
    return True

def test_delta_lake_functionality(spark):
    """
    Business Purpose: Test Delta Lake functionality for data storage
    """
    logger.info("Testing Delta Lake functionality...")
    
    try:
        # Create a test Delta table
        test_path = f"{config.BRONZE_PATH}/test_delta"
        
        test_df = spark.range(100).withColumn("test_value", col("id") * 2)
        
        # Write as Delta table
        test_df.write \
            .format("delta") \
            .mode("overwrite") \
            .save(test_path)
        
        # Read back from Delta table
        read_df = spark.read.format("delta").load(test_path)
        count = read_df.count()
        
        logger.info(f"✅ Delta Lake test successful - Wrote and read {count} records")
        
        # Clean up test data
        import shutil
        if os.path.exists(test_path):
            shutil.rmtree(test_path)
            logger.info("✅ Test data cleaned up")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Delta Lake test failed: {str(e)}")
        return False

def run_comprehensive_test():
    """
    Business Purpose: Run all tests to verify Docker setup is ready for pipeline
    """
    logger.info("🐳 DOCKER BANKING PIPELINE SETUP TEST")
    logger.info("=" * 50)
    logger.info("Business Purpose: Verify environment is ready for banking data pipeline")
    
    test_results = {
        "spark_init": False,
        "data_access": False,
        "data_loading": False,
        "directory_creation": False,
        "delta_lake": False
    }
    
    # Test 1: Spark initialization
    spark = test_spark_initialization()
    test_results["spark_init"] = spark is not None
    
    if not spark:
        logger.error("❌ Cannot proceed without Spark. Please check your setup.")
        return test_results
    
    # Test 2: Data file accessibility
    file_paths = test_data_file_accessibility()
    test_results["data_access"] = len(file_paths) > 0
    
    # Test 3: Data loading
    if file_paths:
        test_results["data_loading"] = test_data_loading(spark, file_paths)
    
    # Test 4: Directory creation
    test_results["directory_creation"] = test_directory_creation()
    
    # Test 5: Delta Lake functionality
    test_results["delta_lake"] = test_delta_lake_functionality(spark)
    
    # Generate summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 TEST RESULTS SUMMARY")
    logger.info("=" * 50)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name.replace('_', ' ').title()}: {status}")
    
    logger.info(f"\nOverall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 ALL TESTS PASSED! Your environment is ready for the banking pipeline.")
        logger.info("You can now run: python docker_pipeline_runner.py")
    else:
        logger.error("⚠️  Some tests failed. Please check the issues above before running the pipeline.")
    
    # Clean up
    spark.stop()
    
    return test_results

def main():
    """
    Business Purpose: Main test execution function
    """
    try:
        results = run_comprehensive_test()
        
        # Exit with appropriate code
        if all(results.values()):
            sys.exit(0)  # Success
        else:
            sys.exit(1)  # Failure
            
    except Exception as e:
        logger.error(f"Test execution failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
