"""
Data Quality Testing Framework
Business Purpose: Ensure data quality standards across all pipeline layers
Validates business rules, data integrity, and compliance requirements
"""

import pytest
from pyspark.sql import SparkSession, DataFrame
from pyspark.sql.functions import *
from pyspark.sql.types import *
import sys
import os
from typing import Dict, List, Tuple

# Add utilities to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'utilities'))
from config import config
from data_quality import DataQualityUtils, BusinessRules

class DataQualityTester:
    """
    Comprehensive data quality testing framework
    Business Purpose: Validate data quality across all pipeline layers
    """
    
    def __init__(self, spark: SparkSession):
        self.spark = spark
        self.config = config
        self.test_results = []
    
    def test_bronze_customer_quality(self, bronze_customer_df: DataFrame) -> Dict:
        """
        Business Context: Validate bronze layer customer data quality
        Why: Ensure customer data meets onboarding and KYC requirements
        What: Test data completeness, format validation, and business rules
        """
        
        print("Testing Bronze Customer Data Quality...")
        
        results = {
            "table_name": "bronze_customer",
            "total_records": bronze_customer_df.count(),
            "tests": []
        }
        
        # Test 1: Customer ID completeness
        null_customer_ids = bronze_customer_df.filter(col("customer_id").isNull()).count()
        results["tests"].append({
            "test_name": "customer_id_completeness",
            "business_rule": "All customers must have unique identifiers for account management",
            "passed": null_customer_ids == 0,
            "failed_records": null_customer_ids,
            "severity": "CRITICAL"
        })
        
        # Test 2: Email format validation
        invalid_emails = bronze_customer_df.filter(
            ~DataQualityUtils.validate_email("email")
        ).count()
        results["tests"].append({
            "test_name": "email_format_validation",
            "business_rule": "Valid email addresses required for customer communication",
            "passed": invalid_emails == 0,
            "failed_records": invalid_emails,
            "severity": "HIGH"
        })
        
        # Test 3: Phone number validation
        invalid_phones = bronze_customer_df.filter(
            ~DataQualityUtils.validate_phone_number("cleaned_phone_number")
        ).count()
        results["tests"].append({
            "test_name": "phone_number_validation",
            "business_rule": "Valid phone numbers required for customer contact",
            "passed": invalid_phones == 0,
            "failed_records": invalid_phones,
            "severity": "HIGH"
        })
        
        # Test 4: Age validation for banking requirements
        underage_customers = bronze_customer_df.filter(col("customer_age") < 18).count()
        results["tests"].append({
            "test_name": "minimum_age_requirement",
            "business_rule": "Customers must be 18+ for banking services",
            "passed": underage_customers == 0,
            "failed_records": underage_customers,
            "severity": "CRITICAL"
        })
        
        # Test 5: Future join date validation
        future_join_dates = bronze_customer_df.filter(
            to_date(col("join_date")) > current_date()
        ).count()
        results["tests"].append({
            "test_name": "join_date_validation",
            "business_rule": "Join dates cannot be in the future",
            "passed": future_join_dates == 0,
            "failed_records": future_join_dates,
            "severity": "MEDIUM"
        })
        
        # Test 6: Channel preference validation
        invalid_channels = bronze_customer_df.filter(
            ~col("standardized_channel").isin(config.VALID_CHANNELS)
        ).count()
        results["tests"].append({
            "test_name": "channel_preference_validation",
            "business_rule": "Customer channel preferences must be valid service channels",
            "passed": invalid_channels == 0,
            "failed_records": invalid_channels,
            "severity": "MEDIUM"
        })
        
        self.test_results.append(results)
        return results
    
    def test_bronze_transaction_quality(self, bronze_transaction_df: DataFrame) -> Dict:
        """
        Business Context: Validate bronze layer transaction data quality
        Why: Ensure transaction integrity for fraud detection and compliance
        What: Test transaction completeness, amount validation, and business rules
        """
        
        print("Testing Bronze Transaction Data Quality...")
        
        results = {
            "table_name": "bronze_transactions",
            "total_records": bronze_transaction_df.count(),
            "tests": []
        }
        
        # Test 1: Transaction ID uniqueness
        total_txns = bronze_transaction_df.count()
        unique_txns = bronze_transaction_df.select("txn_id").distinct().count()
        results["tests"].append({
            "test_name": "transaction_id_uniqueness",
            "business_rule": "Transaction IDs must be unique for audit trail",
            "passed": total_txns == unique_txns,
            "failed_records": total_txns - unique_txns,
            "severity": "CRITICAL"
        })
        
        # Test 2: Transaction amount validation
        invalid_amounts = bronze_transaction_df.filter(
            ~DataQualityUtils.validate_transaction_amount("txn_amount")
        ).count()
        results["tests"].append({
            "test_name": "transaction_amount_validation",
            "business_rule": "Transaction amounts must be within business limits",
            "passed": invalid_amounts == 0,
            "failed_records": invalid_amounts,
            "severity": "HIGH"
        })
        
        # Test 3: Account type validation
        invalid_account_types = bronze_transaction_df.filter(
            ~col("account_type").isin(config.VALID_ACCOUNT_TYPES)
        ).count()
        results["tests"].append({
            "test_name": "account_type_validation",
            "business_rule": "Account types must be valid banking products",
            "passed": invalid_account_types == 0,
            "failed_records": invalid_account_types,
            "severity": "HIGH"
        })
        
        # Test 4: Transaction date validation
        future_transactions = bronze_transaction_df.filter(
            col("txn_date") > current_date()
        ).count()
        results["tests"].append({
            "test_name": "transaction_date_validation",
            "business_rule": "Transaction dates cannot be in the future",
            "passed": future_transactions == 0,
            "failed_records": future_transactions,
            "severity": "CRITICAL"
        })
        
        # Test 5: Channel validation
        invalid_txn_channels = bronze_transaction_df.filter(
            ~col("txn_channel").isin(config.VALID_CHANNELS)
        ).count()
        results["tests"].append({
            "test_name": "transaction_channel_validation",
            "business_rule": "Transaction channels must be valid service channels",
            "passed": invalid_txn_channels == 0,
            "failed_records": invalid_txn_channels,
            "severity": "MEDIUM"
        })
        
        # Test 6: Suspicious transaction detection
        suspicious_count = bronze_transaction_df.filter(
            col("is_suspicious_amount") == True
        ).count()
        suspicious_rate = (suspicious_count / total_txns * 100) if total_txns > 0 else 0
        results["tests"].append({
            "test_name": "suspicious_transaction_rate",
            "business_rule": "Suspicious transaction rate should be below 5% threshold",
            "passed": suspicious_rate < 5.0,
            "failed_records": suspicious_count,
            "severity": "HIGH",
            "metric_value": suspicious_rate
        })
        
        self.test_results.append(results)
        return results
    
    def test_silver_enrichment_quality(self, silver_customer_df: DataFrame, silver_transaction_df: DataFrame) -> Dict:
        """
        Business Context: Validate silver layer enrichment quality
        Why: Ensure enriched data supports accurate analytics and ML features
        What: Test join completeness, feature calculation, and enrichment logic
        """
        
        print("Testing Silver Layer Enrichment Quality...")
        
        results = {
            "table_name": "silver_enrichment",
            "total_records": silver_transaction_df.count(),
            "tests": []
        }
        
        # Test 1: Customer context join completeness
        missing_customer_context = silver_transaction_df.filter(
            col("customer_name").isNull()
        ).count()
        results["tests"].append({
            "test_name": "customer_context_completeness",
            "business_rule": "All transactions should have customer context for analytics",
            "passed": missing_customer_context == 0,
            "failed_records": missing_customer_context,
            "severity": "HIGH"
        })
        
        # Test 2: Age segment calculation
        invalid_age_segments = silver_customer_df.filter(
            col("age_segment").isNull() | (col("age_segment") == "")
        ).count()
        results["tests"].append({
            "test_name": "age_segment_calculation",
            "business_rule": "All customers should have valid age segments for targeting",
            "passed": invalid_age_segments == 0,
            "failed_records": invalid_age_segments,
            "severity": "MEDIUM"
        })
        
        # Test 3: Digital adoption score calculation
        invalid_digital_scores = silver_customer_df.filter(
            col("digital_adoption_score").isNull() | 
            (col("digital_adoption_score") < 0) | 
            (col("digital_adoption_score") > 4)
        ).count()
        results["tests"].append({
            "test_name": "digital_adoption_score_validation",
            "business_rule": "Digital adoption scores must be between 0-4",
            "passed": invalid_digital_scores == 0,
            "failed_records": invalid_digital_scores,
            "severity": "MEDIUM"
        })
        
        # Test 4: Transaction frequency score logic
        invalid_frequency_scores = silver_transaction_df.filter(
            col("txn_frequency_score").isNull() |
            (col("txn_frequency_score") < 1) |
            (col("txn_frequency_score") > 5)
        ).count()
        results["tests"].append({
            "test_name": "transaction_frequency_score_validation",
            "business_rule": "Transaction frequency scores must be between 1-5",
            "passed": invalid_frequency_scores == 0,
            "failed_records": invalid_frequency_scores,
            "severity": "MEDIUM"
        })
        
        # Test 5: Risk score calculation
        invalid_risk_scores = silver_transaction_df.filter(
            col("transaction_risk_score").isNull() |
            (col("transaction_risk_score") < 0)
        ).count()
        results["tests"].append({
            "test_name": "risk_score_calculation",
            "business_rule": "Risk scores must be non-negative values",
            "passed": invalid_risk_scores == 0,
            "failed_records": invalid_risk_scores,
            "severity": "HIGH"
        })
        
        self.test_results.append(results)
        return results
    
    def test_gold_business_metrics(self, gold_customer_analytics_df: DataFrame, gold_monthly_kpi_df: DataFrame) -> Dict:
        """
        Business Context: Validate gold layer business metrics
        Why: Ensure business KPIs are calculated correctly for executive reporting
        What: Test metric calculations, business logic, and data consistency
        """
        
        print("Testing Gold Layer Business Metrics...")
        
        results = {
            "table_name": "gold_business_metrics",
            "total_records": gold_customer_analytics_df.count(),
            "tests": []
        }
        
        # Test 1: Customer Lifetime Value calculation
        negative_clv = gold_customer_analytics_df.filter(
            col("estimated_clv") < 0
        ).count()
        results["tests"].append({
            "test_name": "customer_lifetime_value_validation",
            "business_rule": "Customer Lifetime Value should not be negative",
            "passed": negative_clv == 0,
            "failed_records": negative_clv,
            "severity": "HIGH"
        })
        
        # Test 2: Engagement score reasonableness
        unreasonable_engagement = gold_customer_analytics_df.filter(
            (col("engagement_score") < 0) | (col("engagement_score") > 1000)
        ).count()
        results["tests"].append({
            "test_name": "engagement_score_reasonableness",
            "business_rule": "Engagement scores should be within reasonable range (0-1000)",
            "passed": unreasonable_engagement == 0,
            "failed_records": unreasonable_engagement,
            "severity": "MEDIUM"
        })
        
        # Test 3: Churn risk score validation
        invalid_churn_risk = gold_customer_analytics_df.filter(
            col("churn_risk_score").isNull() |
            (col("churn_risk_score") < 1) |
            (col("churn_risk_score") > 5)
        ).count()
        results["tests"].append({
            "test_name": "churn_risk_score_validation",
            "business_rule": "Churn risk scores must be between 1-5",
            "passed": invalid_churn_risk == 0,
            "failed_records": invalid_churn_risk,
            "severity": "HIGH"
        })
        
        # Test 4: Monthly KPI completeness
        if gold_monthly_kpi_df.count() > 0:
            missing_kpi_values = gold_monthly_kpi_df.filter(
                col("total_revenue").isNull() | 
                col("new_customers").isNull() |
                col("active_customers").isNull()
            ).count()
            results["tests"].append({
                "test_name": "monthly_kpi_completeness",
                "business_rule": "Monthly KPIs must have complete revenue and customer metrics",
                "passed": missing_kpi_values == 0,
                "failed_records": missing_kpi_values,
                "severity": "CRITICAL"
            })
        
        # Test 5: Digital adoption rate validation
        if gold_monthly_kpi_df.count() > 0:
            invalid_adoption_rates = gold_monthly_kpi_df.filter(
                (col("digital_adoption_rate") < 0) | 
                (col("digital_adoption_rate") > 100)
            ).count()
            results["tests"].append({
                "test_name": "digital_adoption_rate_validation",
                "business_rule": "Digital adoption rates must be between 0-100%",
                "passed": invalid_adoption_rates == 0,
                "failed_records": invalid_adoption_rates,
                "severity": "MEDIUM"
            })
        
        self.test_results.append(results)
        return results
    
    def generate_test_report(self) -> Dict:
        """
        Business Purpose: Generate comprehensive test report for stakeholders
        """
        
        print("Generating Data Quality Test Report...")
        
        total_tests = sum(len(result["tests"]) for result in self.test_results)
        passed_tests = sum(
            sum(1 for test in result["tests"] if test["passed"]) 
            for result in self.test_results
        )
        failed_tests = total_tests - passed_tests
        
        critical_failures = sum(
            sum(1 for test in result["tests"] if not test["passed"] and test["severity"] == "CRITICAL")
            for result in self.test_results
        )
        
        report = {
            "test_summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                "critical_failures": critical_failures
            },
            "detailed_results": self.test_results,
            "recommendations": self._generate_recommendations(),
            "report_timestamp": str(self.spark.sql("SELECT current_timestamp()").collect()[0][0])
        }
        
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """
        Business Purpose: Generate actionable recommendations based on test results
        """
        
        recommendations = []
        
        for result in self.test_results:
            for test in result["tests"]:
                if not test["passed"]:
                    if test["severity"] == "CRITICAL":
                        recommendations.append(
                            f"URGENT: Fix {test['test_name']} in {result['table_name']} - "
                            f"{test['business_rule']} ({test['failed_records']} records affected)"
                        )
                    elif test["severity"] == "HIGH":
                        recommendations.append(
                            f"HIGH PRIORITY: Address {test['test_name']} in {result['table_name']} - "
                            f"{test['failed_records']} records need attention"
                        )
        
        if not recommendations:
            recommendations.append("All data quality tests passed successfully!")
        
        return recommendations

# Example usage function
def run_data_quality_tests(spark: SparkSession, table_paths: Dict[str, str]) -> Dict:
    """
    Business Purpose: Execute comprehensive data quality testing
    """
    
    tester = DataQualityTester(spark)
    
    # Load test data
    bronze_customer_df = spark.read.format("delta").load(table_paths["bronze_customer"])
    bronze_transaction_df = spark.read.format("delta").load(table_paths["bronze_transaction"])
    silver_customer_df = spark.read.format("delta").load(table_paths["silver_customer"])
    silver_transaction_df = spark.read.format("delta").load(table_paths["silver_transaction"])
    gold_customer_analytics_df = spark.read.format("delta").load(table_paths["gold_customer_analytics"])
    gold_monthly_kpi_df = spark.read.format("delta").load(table_paths["gold_monthly_kpi"])
    
    # Run all tests
    tester.test_bronze_customer_quality(bronze_customer_df)
    tester.test_bronze_transaction_quality(bronze_transaction_df)
    tester.test_silver_enrichment_quality(silver_customer_df, silver_transaction_df)
    tester.test_gold_business_metrics(gold_customer_analytics_df, gold_monthly_kpi_df)
    
    # Generate report
    return tester.generate_test_report()
