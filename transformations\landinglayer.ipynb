{"cells": [{"cell_type": "markdown", "id": "8658613b", "metadata": {}, "source": ["## **Customer 2023 Data Ingestion**"]}, {"cell_type": "code", "execution_count": null, "id": "2d171618", "metadata": {}, "outputs": [], "source": ["import dlt"]}, {"cell_type": "code", "execution_count": 1, "id": "434fd9a1", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "            <div>\n", "                <p><b>SparkSession - in-memory</b></p>\n", "                \n", "        <div>\n", "            <p><b>SparkContext</b></p>\n", "\n", "            <p><a href=\"http://SaiSunrisers:4040\">Spark UI</a></p>\n", "\n", "            <dl>\n", "              <dt>Version</dt>\n", "                <dd><code>v3.5.6</code></dd>\n", "              <dt>Master</dt>\n", "                <dd><code>local[*]</code></dd>\n", "              <dt>AppName</dt>\n", "                <dd><code>DLT Spark</code></dd>\n", "            </dl>\n", "        </div>\n", "        \n", "            </div>\n", "        "], "text/plain": ["<pyspark.sql.session.SparkSession at 0x2ab332bb810>"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from pyspark.sql import SparkSession\n", "from pyspark.sql.functions import *\n", "from pyspark.sql.types import *\n", "spark = SparkSession.builder.appName(\"DLT Spark\").getOrCreate()\n", "spark\n"]}, {"cell_type": "code", "execution_count": 2, "id": "944ea5f1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- account_id: long (nullable = true)\n", " |-- customer_id: long (nullable = true)\n", " |-- account_type: string (nullable = true)\n", " |-- balance: double (nullable = true)\n", " |-- txn_id: long (nullable = true)\n", " |-- txn_date: date (nullable = true)\n", " |-- txn_type: string (nullable = true)\n", " |-- txn_amount: double (nullable = true)\n", " |-- txn_channel: string (nullable = true)\n", "\n", "+----------+-----------+------------+--------+------+----------+--------+----------+-----------+\n", "|account_id|customer_id|account_type|balance |txn_id|txn_date  |txn_type|txn_amount|txn_channel|\n", "+----------+-----------+------------+--------+------+----------+--------+----------+-----------+\n", "|100010    |1          |Current     |500.0   |3     |2023-07-28|Debit   |-11.22    |Branch     |\n", "|100010    |1          |Current     |600.0   |1     |2023-09-02|Debit   |-285.74   |ATM        |\n", "|100010    |1          |Current     |700.0   |2     |2023-09-25|Debit   |258.57    |Online     |\n", "|100010    |1          |Current     |800.0   |4     |2023-12-08|Debit   |-87.41    |Mobile     |\n", "|100011    |1          |Savings     |3453.22 |5     |2023-07-14|Debit   |1.0       |Mobile     |\n", "|100011    |1          |Savings     |3453.22 |6     |2023-07-22|Debit   |1.0       |Mobile     |\n", "|100012    |1          |Current     |-1022.97|7     |2022-01-16|Credit  |172.07    |Branch     |\n", "|100012    |1          |Current     |-1022.97|9     |2023-03-28|Debit   |-12.51    |Online     |\n", "|100012    |1          |Current     |-1022.97|8     |2023-11-18|Credit  |78.02     |ATM        |\n", "|100020    |2          |Savings     |-229.6  |14    |2023-02-06|Debit   |-35.35    |Online     |\n", "+----------+-----------+------------+--------+------+----------+--------+----------+-----------+\n", "only showing top 10 rows\n", "\n"]}], "source": ["from pathlib import Path\n", "from pyspark.sql.functions import *\n", "from pyspark.sql.types import *\n", "# Define data path (cross-platform safe)\n", "# data_path = Path(r\"E:\\Study Space\\Data\\TPCH_SF10\\customer.parquet\")\n", "data_path = r\"E:\\Study Space\\Analytics Enginerring\\Data Engineering\\Azure Databricks\\Lakeflow Declarative Pipeline\\DLT_BANKING_PROJECT\\data\\accounts_transactions_2023_set1.csv\"\n", "\n", "accounts_schema = StructType(\n", "    [\n", "        StructField('account_id', LongType(), True), \n", "        StructField('customer_id', LongType(), True), \n", "        StructField('account_type', StringType(), True), \n", "        StructField('balance', DoubleType(), True), \n", "        StructField('txn_id', LongType(), True), \n", "        StructField('txn_date', DateType(), True), \n", "        StructField('txn_type', StringType(), True), \n", "        StructField('txn_amount', DoubleType(), True), \n", "        StructField('txn_channel', StringType(), True)\n", "        ])\n", "\n", "# Load customer data from Parquet\n", "accounts_df = (\n", "    spark.read\n", "        .option('header', 'true') \n", "        .schema(accounts_schema)\n", "        .csv(data_path)\n", ")\n", "\n", "# Display schema and sample records\n", "accounts_df.printSchema()\n", "accounts_df.show(10, truncate=False)"]}, {"cell_type": "code", "execution_count": 15, "id": "a704dc53", "metadata": {}, "outputs": [{"data": {"text/plain": ["StructType([<PERSON>ruct<PERSON>ield('account_id', StringType(), True), StructField('customer_id', StringType(), True), StructField('account_type', StringType(), True), StructField('balance', StringType(), True), StructField('txn_id', StringType(), True), StructField('txn_date', StringType(), True), StructField('txn_type', StringType(), True), StructField('txn_amount', StringType(), True), StructField('txn_channel', StringType(), True)])"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["customer_df.schema"]}, {"cell_type": "code", "execution_count": null, "id": "b8a4479d", "metadata": {}, "outputs": [], "source": ["customer_schema = StructType(\n", "    [\n", "        StructField('customer_id', StringType(), True), \n", "        StructField('name', StringType(), True), \n", "        StructField('dob', StringType(), True), \n", "        Struct<PERSON>ield('gender', StringType(), True), \n", "        StructField('city', StringType(), True), \n", "        StructField('join_date', StringType(), True), \n", "        StructField('status', StringType(), True), \n", "        StructField('email', StringType(), True), \n", "        StructField('phone_number', StringType(), True), \n", "        StructField('channel', StringType(), True), \n", "        <PERSON><PERSON><PERSON><PERSON><PERSON>('occupation', StringType(), True), \n", "        StructField('income_range', StringType(), True), \n", "        StructField('risk_segment', StringType(), True)\n", "    ])\n", "\n", "\n", "@dlt.table(\n", "    name=\"landing_customer_incremental\",\n", "    comment=\"Incremental landing table for customer data\",\n", ")\n", "\n", "def landing_customer_incremental():\n", "    return (\n", "        spark.readStream.format(\"cloudFiles\")\n", "        .option(\"cloudFiles.format\", \"csv\")\n", "        .option(\"header\", \"true\")\n", "        .schema(customer_schema)\n", "        .load(\"/Volumes/dlt_banking_project_catalog/dlt_bank_project_schema/raw/customer/2023\")\n", "    )\n"]}, {"cell_type": "markdown", "id": "********", "metadata": {}, "source": ["## **Accounts Data 2023 Data Ingestion**"]}, {"cell_type": "code", "execution_count": null, "id": "ae068e9b", "metadata": {}, "outputs": [], "source": ["accounts_schema = StructType(\n", "    [\n", "        StructField('account_id', LongType(), True), \n", "        StructField('customer_id', LongType(), True), \n", "        StructField('account_type', StringType(), True), \n", "        StructField('balance', DoubleType(), True), \n", "        StructField('txn_id', LongType(), True), \n", "        StructField('txn_date', DateType(), True), \n", "        StructField('txn_type', StringType(), True), \n", "        StructField('txn_amount', DoubleType(), True), \n", "        StructField('txn_channel', StringType(), True)\n", "        ])\n", "\n", "@dlt.table(\n", "    name=\"landing_accounts_transactions_incremental\",\n", "    comment = \"Accounts Data Incremental Landing Layer\"\n", ")\n", "\n", "def landing_accounts_transactions_incremental():\n", "    return (\n", "        spark.readStream\n", "            .format('cloudFiles')\n", "            .option('cloudFiles.format', 'csv')\n", "            .option('cloudFiles.schemaLocation', f'{checkpoint_path}/accounts_schema')\n", "            .schema(accounts_schema)\n", "            .load(\"/Volumes/dlt_banking_project_catalog/dlt_bank_project_schema/raw/accounts_transactions/2023\")\n", "    )"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}