"""
Silver Layer - Delta Live Tables Implementation
Business Purpose: Create analytics-ready datasets with enriched customer and transaction data
This layer provides clean, joined, and enriched data for business analytics and ML features
"""

import dlt
from pyspark.sql.functions import *
from pyspark.sql.types import *
from pyspark.sql.window import Window
import sys
import os

# Add utilities to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'utilities'))
from config import config
from data_quality import DataQualityUtils, BusinessRules

# ============================================================================
# SILVER CUSTOMER DATA - ENRICHED AND ANALYTICS-READY
# ============================================================================

@dlt.table(
    name=config.SILVER_CUSTOMER_TABLE,
    comment="""
    Silver layer customer data enriched with analytics features and business metrics.
    Business Purpose: Provide analytics-ready customer data for segmentation,
    lifetime value analysis, and personalized banking services.
    """,
    table_properties={
        "quality": "silver",
        "pipelines.autoOptimize.managed": "true"
    }
)
def silver_customer_enriched():
    """
    Business Context: Enrich customer data with analytics features for business insights
    Why: Enable customer segmentation, risk assessment, and personalized services
    What: Customer demographics with calculated metrics and business intelligence features
    """
    
    # Read bronze customer data
    customer_df = dlt.read(config.BRONZE_CUSTOMER_TABLE)
    
    # Business Enhancement: Add customer lifecycle features
    customer_df = customer_df.withColumn("days_since_joining", 
        datediff(current_date(), to_date(col("join_date"))))
    
    customer_df = customer_df.withColumn("customer_tenure_category",
        when(col("days_since_joining") <= 90, "NEW")
        .when(col("days_since_joining") <= 365, "RECENT")
        .when(col("days_since_joining") <= 1095, "ESTABLISHED")
        .otherwise("LONG_TERM")
    )
    
    # Business Enhancement: Age-based customer segmentation
    customer_df = customer_df.withColumn("age_segment",
        when(col("customer_age") <= 25, "YOUNG_ADULT")
        .when(col("customer_age") <= 35, "MILLENNIAL")
        .when(col("customer_age") <= 50, "GEN_X")
        .when(col("customer_age") <= 65, "BABY_BOOMER")
        .otherwise("SENIOR")
    )
    
    # Business Enhancement: Income-based targeting segments
    customer_df = customer_df.withColumn("income_segment_numeric",
        when(col("income_range") == "LOW", 1)
        .when(col("income_range") == "MEDIUM", 2)
        .when(col("income_range") == "HIGH", 3)
        .otherwise(0)
    )
    
    # Business Enhancement: Digital adoption score based on preferred channel
    customer_df = customer_df.withColumn("digital_adoption_score",
        when(col("standardized_channel") == "MOBILE", 4)
        .when(col("standardized_channel") == "ONLINE", 3)
        .when(col("standardized_channel") == "ATM", 2)
        .when(col("standardized_channel") == "BRANCH", 1)
        .otherwise(0)
    )
    
    # Business Enhancement: Risk score calculation
    customer_df = BusinessRules.calculate_risk_score(customer_df)
    
    # Add silver layer metadata
    customer_df = customer_df.withColumn("silver_processed_timestamp", current_timestamp()) \
                           .withColumn("silver_processed_date", current_date())
    
    return customer_df

# ============================================================================
# SILVER TRANSACTIONS DATA - ENRICHED WITH CUSTOMER CONTEXT
# ============================================================================

@dlt.table(
    name=config.SILVER_TRANSACTIONS_TABLE,
    comment="""
    Silver layer transactions enriched with customer context and analytics features.
    Business Purpose: Provide transaction data with customer insights for fraud detection,
    spending analysis, and behavioral analytics.
    """,
    table_properties={
        "quality": "silver",
        "pipelines.autoOptimize.managed": "true"
    }
)
def silver_transactions_enriched():
    """
    Business Context: Enrich transactions with customer context for advanced analytics
    Why: Enable fraud detection, spending pattern analysis, and customer behavior insights
    What: Transactions joined with customer data and enriched with analytical features
    """
    
    # Read bronze data
    transactions_df = dlt.read(config.BRONZE_TRANSACTIONS_TABLE)
    customer_df = dlt.read(config.BRONZE_CUSTOMER_TABLE)
    
    # Business Enhancement: Join transactions with customer context
    enriched_df = transactions_df.alias("t").join(
        customer_df.alias("c"),
        col("t.customer_id") == col("c.customer_id"),
        "left"
    ).select(
        col("t.*"),
        col("c.name").alias("customer_name"),
        col("c.city").alias("customer_city"),
        col("c.standardized_status").alias("customer_status"),
        col("c.customer_age"),
        col("c.income_range"),
        col("c.risk_segment"),
        col("c.standardized_channel").alias("customer_preferred_channel")
    )
    
    # Business Enhancement: Calculate customer transaction patterns
    window_spec = Window.partitionBy("customer_id").orderBy("txn_date")
    
    enriched_df = enriched_df.withColumn("customer_txn_sequence", 
        row_number().over(window_spec))
    
    enriched_df = enriched_df.withColumn("days_since_last_txn",
        datediff(col("txn_date"), 
                lag(col("txn_date"), 1).over(window_spec)))
    
    # Business Enhancement: Transaction velocity analysis
    enriched_df = enriched_df.withColumn("txn_frequency_score",
        when(col("days_since_last_txn") <= 1, 5)
        .when(col("days_since_last_txn") <= 7, 4)
        .when(col("days_since_last_txn") <= 30, 3)
        .when(col("days_since_last_txn") <= 90, 2)
        .otherwise(1)
    )
    
    # Business Enhancement: Cross-channel behavior analysis
    enriched_df = enriched_df.withColumn("channel_consistency_flag",
        when(col("txn_channel") == col("customer_preferred_channel"), "CONSISTENT")
        .otherwise("DIFFERENT")
    )
    
    # Business Enhancement: Risk-based transaction scoring
    enriched_df = enriched_df.withColumn("transaction_risk_score",
        when(col("is_suspicious_amount") == True, 3)
        .when(col("txn_category") == "VERY_LARGE", 2)
        .when(col("channel_consistency_flag") == "DIFFERENT", 1)
        .otherwise(0)
    )
    
    # Business Enhancement: Time-based analytics features
    enriched_df = enriched_df.withColumn("is_weekend",
        when(col("txn_day_of_week").isin([1, 7]), True).otherwise(False))
    
    enriched_df = enriched_df.withColumn("time_of_day_category",
        when(col("txn_hour").between(6, 11), "MORNING")
        .when(col("txn_hour").between(12, 17), "AFTERNOON")
        .when(col("txn_hour").between(18, 22), "EVENING")
        .otherwise("NIGHT")
    )
    
    # Add silver layer metadata
    enriched_df = enriched_df.withColumn("silver_processed_timestamp", current_timestamp()) \
                           .withColumn("silver_processed_date", current_date())
    
    return enriched_df

# ============================================================================
# SILVER ACCOUNT SUMMARY - AGGREGATED ACCOUNT METRICS
# ============================================================================

@dlt.table(
    name=config.SILVER_ACCOUNTS_TABLE,
    comment="""
    Silver layer account summary with aggregated metrics and KPIs.
    Business Purpose: Provide account-level insights for relationship management,
    product recommendations, and portfolio analysis.
    """,
    table_properties={
        "quality": "silver",
        "pipelines.autoOptimize.managed": "true"
    }
)
def silver_accounts_summary():
    """
    Business Context: Create account-level summaries for relationship banking
    Why: Enable account managers to understand customer relationships and product usage
    What: Account metrics, transaction summaries, and relationship indicators
    """
    
    # Read silver transactions data
    transactions_df = dlt.read(config.SILVER_TRANSACTIONS_TABLE)
    
    # Business Analytics: Account-level aggregations
    account_summary = transactions_df.groupBy(
        "account_id", 
        "customer_id", 
        "customer_name",
        "account_type",
        "customer_city",
        "customer_status",
        "income_range",
        "risk_segment"
    ).agg(
        # Transaction volume metrics
        count("txn_id").alias("total_transactions"),
        countDistinct("txn_date").alias("active_days"),
        sum("standardized_txn_amount").alias("net_transaction_amount"),
        avg("standardized_txn_amount").alias("avg_transaction_amount"),
        
        # Balance and financial metrics
        last("balance").alias("current_balance"),
        max("balance").alias("max_balance"),
        min("balance").alias("min_balance"),
        
        # Channel usage patterns
        countDistinct("txn_channel").alias("channels_used"),
        first("txn_channel").alias("most_used_channel"),  # Using first instead of mode for compatibility
        
        # Risk and fraud indicators
        sum(when(col("is_suspicious_amount") == True, 1).otherwise(0)).alias("suspicious_transactions"),
        sum(when(col("transaction_risk_score") > 0, 1).otherwise(0)).alias("risky_transactions"),
        avg("transaction_risk_score").alias("avg_risk_score"),
        
        # Behavioral patterns
        sum(when(col("is_weekend") == True, 1).otherwise(0)).alias("weekend_transactions"),
        avg("txn_frequency_score").alias("avg_frequency_score"),
        
        # Date ranges
        min("txn_date").alias("first_transaction_date"),
        max("txn_date").alias("last_transaction_date")
    )
    
    # Business Enhancement: Calculate derived metrics
    account_summary = account_summary.withColumn("days_between_first_last_txn",
        datediff(col("last_transaction_date"), col("first_transaction_date")))
    
    account_summary = account_summary.withColumn("transaction_frequency",
        when(col("days_between_first_last_txn") > 0, 
             col("total_transactions") / col("days_between_first_last_txn"))
        .otherwise(0))
    
    # Business Enhancement: Account health scoring
    account_summary = account_summary.withColumn("account_health_score",
        when(col("current_balance") > 0, 2).otherwise(0) +
        when(col("total_transactions") > 10, 2).otherwise(0) +
        when(col("avg_frequency_score") > 3, 1).otherwise(0) +
        when(col("suspicious_transactions") == 0, 1).otherwise(-1)
    )
    
    # Business Enhancement: Customer value segmentation
    account_summary = account_summary.withColumn("customer_value_segment",
        when((col("current_balance") > 10000) & (col("total_transactions") > 50), "HIGH_VALUE")
        .when((col("current_balance") > 5000) & (col("total_transactions") > 20), "MEDIUM_VALUE")
        .when(col("current_balance") > 0, "LOW_VALUE")
        .otherwise("NEGATIVE_VALUE")
    )
    
    # Add silver layer metadata
    account_summary = account_summary.withColumn("silver_processed_timestamp", current_timestamp()) \
                                   .withColumn("silver_processed_date", current_date())
    
    return account_summary

# ============================================================================
# SILVER DATA QUALITY MONITORING
# ============================================================================

@dlt.table(
    name="silver_data_quality_metrics",
    comment="""
    Data quality metrics for silver layer monitoring and alerting.
    Business Purpose: Ensure silver layer data meets quality standards
    for downstream analytics and reporting.
    """
)
def silver_data_quality_metrics():
    """
    Business Context: Monitor silver layer data quality for analytics reliability
    Why: Ensure high-quality data for business decisions and regulatory compliance
    What: Quality metrics, completeness checks, and anomaly detection
    """
    
    customer_df = dlt.read(config.SILVER_CUSTOMER_TABLE)
    transactions_df = dlt.read(config.SILVER_TRANSACTIONS_TABLE)
    accounts_df = dlt.read(config.SILVER_ACCOUNTS_TABLE)
    
    # Customer quality metrics
    customer_metrics = customer_df.agg(
        lit("silver_customers").alias("table_name"),
        count("*").alias("total_records"),
        sum(when(col("customer_age").isNull(), 1).otherwise(0)).alias("missing_age"),
        sum(when(col("digital_adoption_score") == 0, 1).otherwise(0)).alias("unknown_channel_preference"),
        avg("digital_adoption_score").alias("avg_digital_adoption")
    )
    
    # Transaction quality metrics
    transaction_metrics = transactions_df.agg(
        lit("silver_transactions").alias("table_name"),
        count("*").alias("total_records"),
        sum(when(col("customer_name").isNull(), 1).otherwise(0)).alias("missing_customer_context"),
        sum(when(col("transaction_risk_score") > 2, 1).otherwise(0)).alias("high_risk_transactions"),
        avg("transaction_risk_score").alias("avg_risk_score")
    )
    
    # Account quality metrics
    account_metrics = accounts_df.agg(
        lit("silver_accounts").alias("table_name"),
        count("*").alias("total_records"),
        sum(when(col("account_health_score") < 0, 1).otherwise(0)).alias("unhealthy_accounts"),
        avg("account_health_score").alias("avg_health_score"),
        sum(when(col("customer_value_segment") == "HIGH_VALUE", 1).otherwise(0)).alias("high_value_customers")
    )
    
    return customer_metrics.unionByName(transaction_metrics, allowMissingColumns=True) \
                          .unionByName(account_metrics, allowMissingColumns=True) \
                          .withColumn("quality_check_timestamp", current_timestamp()) \
                          .withColumn("quality_check_date", current_date())
