"""
Data Quality Utilities for Banking Pipeline
Contains reusable data quality functions and validation rules
"""

from pyspark.sql import DataFrame
from pyspark.sql.functions import *
from pyspark.sql.types import *
import re
from typing import Dict, List, Tuple

class DataQualityUtils:
    """Utility class for data quality operations"""
    
    @staticmethod
    def validate_email(email_col: str) -> Column:
        """
        Business Purpose: Ensure customer email addresses are valid for communication
        Validates email format using regex pattern
        """
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return col(email_col).rlike(email_pattern)
    
    @staticmethod
    def clean_phone_number(phone_col: str) -> Column:
        """
        Business Purpose: Standardize phone numbers for customer contact
        Removes all non-numeric characters and validates length
        """
        return regexp_replace(col(phone_col), r"[^0-9]", "")
    
    @staticmethod
    def validate_phone_number(phone_col: str, min_length: int = 10, max_length: int = 15) -> Column:
        """
        Business Purpose: Ensure phone numbers are valid for customer communication
        Validates phone number length after cleaning
        """
        return col(phone_col).rlike(f"^\\d{{{min_length},{max_length}}}$")
    
    @staticmethod
    def standardize_gender(gender_col: str) -> Column:
        """
        Business Purpose: Standardize gender values for demographic analysis
        Converts various gender representations to standard format
        """
        return when(upper(col(gender_col)).isin(['M', 'MALE']), 'MALE') \
               .when(upper(col(gender_col)).isin(['F', 'FEMALE']), 'FEMALE') \
               .otherwise('UNKNOWN')
    
    @staticmethod
    def standardize_status(status_col: str) -> Column:
        """
        Business Purpose: Ensure consistent customer status for account management
        Standardizes status values and handles nulls
        """
        return when(col(status_col).isNull() | (trim(col(status_col)) == ""), 'ACTIVE') \
               .otherwise(upper(trim(col(status_col))))
    
    @staticmethod
    def validate_date_range(date_col: str, start_date: str = "1900-01-01", end_date: str = "2030-12-31") -> Column:
        """
        Business Purpose: Ensure dates are within reasonable business ranges
        Validates date values are within acceptable range
        """
        return (col(date_col) >= lit(start_date)) & (col(date_col) <= lit(end_date))
    
    @staticmethod
    def calculate_age(dob_col: str, reference_date: str = None) -> Column:
        """
        Business Purpose: Calculate customer age for demographic analysis and product targeting
        Calculates age from date of birth
        """
        if reference_date is None:
            reference_date = current_date()
        else:
            reference_date = lit(reference_date)
        
        return floor(datediff(reference_date, to_date(col(dob_col))) / 365.25)
    
    @staticmethod
    def validate_transaction_amount(amount_col: str, min_amount: float = 0.01, max_amount: float = 1000000.0) -> Column:
        """
        Business Purpose: Detect potentially fraudulent or erroneous transactions
        Validates transaction amounts are within reasonable business limits
        """
        return (abs(col(amount_col)) >= min_amount) & (abs(col(amount_col)) <= max_amount)
    
    @staticmethod
    def detect_suspicious_transactions(amount_col: str, threshold_multiplier: float = 3.0) -> Column:
        """
        Business Purpose: Flag potentially fraudulent transactions for review
        Identifies transactions that are significantly above average
        """
        # This would typically use window functions to compare against customer's average
        # Simplified version for demonstration
        return abs(col(amount_col)) > (threshold_multiplier * 1000)
    
    @staticmethod
    def standardize_text_fields(text_col: str) -> Column:
        """
        Business Purpose: Ensure consistent text formatting for data analysis
        Standardizes text fields by trimming and converting to uppercase
        """
        return upper(trim(col(text_col)))
    
    @staticmethod
    def add_data_quality_flags(df: DataFrame, table_name: str) -> DataFrame:
        """
        Business Purpose: Track data quality issues for monitoring and improvement
        Adds data quality flags to identify records with issues
        """
        return df.withColumn("dq_source_table", lit(table_name)) \
                .withColumn("dq_processed_timestamp", current_timestamp()) \
                .withColumn("dq_record_hash", sha2(concat_ws("|", *df.columns), 256))
    
    @staticmethod
    def get_data_quality_summary(df: DataFrame, key_columns: List[str]) -> Dict:
        """
        Business Purpose: Monitor data quality metrics for pipeline health
        Returns summary statistics for data quality monitoring
        """
        total_records = df.count()
        
        summary = {
            "total_records": total_records,
            "duplicate_records": df.count() - df.dropDuplicates(key_columns).count(),
            "null_key_columns": {}
        }
        
        for col_name in key_columns:
            null_count = df.filter(col(col_name).isNull()).count()
            summary["null_key_columns"][col_name] = {
                "null_count": null_count,
                "null_percentage": (null_count / total_records * 100) if total_records > 0 else 0
            }
        
        return summary

class BusinessRules:
    """Business-specific validation rules for banking domain"""
    
    @staticmethod
    def validate_customer_onboarding_rules(df: DataFrame) -> DataFrame:
        """
        Business Purpose: Ensure new customers meet onboarding requirements
        Validates that customers have all required information for account opening
        """
        return df.filter(
            col("customer_id").isNotNull() &
            col("name").isNotNull() &
            col("email").isNotNull() &
            col("phone_number").isNotNull() &
            col("dob").isNotNull() &
            (col("join_date") <= current_date())
        )
    
    @staticmethod
    def validate_transaction_business_rules(df: DataFrame) -> DataFrame:
        """
        Business Purpose: Ensure transactions follow business logic
        Validates transaction consistency and business rules
        """
        return df.filter(
            col("txn_id").isNotNull() &
            col("account_id").isNotNull() &
            col("customer_id").isNotNull() &
            col("txn_amount").isNotNull() &
            col("txn_date").isNotNull() &
            (col("txn_date") <= current_date())
        )
    
    @staticmethod
    def calculate_risk_score(df: DataFrame) -> DataFrame:
        """
        Business Purpose: Calculate customer risk scores for compliance and lending
        Assigns risk scores based on transaction patterns and customer profile
        """
        return df.withColumn("calculated_risk_score",
            when(col("risk_segment") == "HIGH", 3)
            .when(col("risk_segment") == "MEDIUM", 2)
            .when(col("risk_segment") == "LOW", 1)
            .otherwise(0)
        )
