"""
Windows Setup Test Script for Banking Pipeline
Business Purpose: Test the banking pipeline setup on Windows before Docker deployment
This script verifies the code structure and configuration without requiring Dock<PERSON>
"""

import os
import sys
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_project_structure():
    """
    Business Purpose: Verify all required project files and directories exist
    """
    logger.info("Testing project structure...")
    
    required_files = [
        "utilities/config.py",
        "utilities/data_quality.py",
        "bronze/bronze_layer_pyspark.py",
        "bronze/bronze_layer_dlt.py",
        "silver/silver_layer_pyspark.py",
        "silver/silver_layer_dlt.py",
        "gold/gold_layer_pyspark.py",
        "gold/gold_layer_dlt.py",
        "docker_pipeline_runner.py",
        "test_docker_setup.py",
        "install_delta_lake.py",
        "docker_setup_guide.md"
    ]
    
    required_directories = [
        "utilities",
        "bronze",
        "silver", 
        "gold",
        "tests",
        "examples",
        ".vscode"
    ]
    
    missing_files = []
    missing_dirs = []
    
    # Check files
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            logger.info(f"✅ Found: {file_path}")
    
    # Check directories
    for dir_path in required_directories:
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_path)
        else:
            logger.info(f"✅ Directory: {dir_path}")
    
    if missing_files:
        logger.error(f"❌ Missing files: {missing_files}")
        return False
    
    if missing_dirs:
        logger.error(f"❌ Missing directories: {missing_dirs}")
        return False
    
    logger.info("✅ All required project files and directories found")
    return True

def test_python_imports():
    """
    Business Purpose: Test that all required Python modules can be imported
    """
    logger.info("Testing Python imports...")
    
    required_modules = [
        ("dataclasses", "dataclass, field"),
        ("typing", "Dict, List"),
        ("pathlib", "Path"),
        ("os", "os"),
        ("sys", "sys")
    ]
    
    optional_modules = [
        ("pyspark.sql", "SparkSession"),
        ("pyspark.sql.functions", "*"),
        ("pyspark.sql.types", "*"),
        ("delta", "delta")
    ]
    
    # Test required modules
    for module_name, import_items in required_modules:
        try:
            exec(f"from {module_name} import {import_items}")
            logger.info(f"✅ Required module: {module_name}")
        except ImportError as e:
            logger.error(f"❌ Missing required module {module_name}: {e}")
            return False
    
    # Test optional modules
    pyspark_available = False
    delta_available = False
    
    for module_name, import_items in optional_modules:
        try:
            exec(f"from {module_name} import {import_items}")
            logger.info(f"✅ Optional module: {module_name}")
            if "pyspark" in module_name:
                pyspark_available = True
            if "delta" in module_name:
                delta_available = True
        except ImportError:
            logger.warning(f"⚠️  Optional module not available: {module_name}")
    
    if pyspark_available:
        logger.info("✅ PySpark is available for local testing")
    else:
        logger.warning("⚠️  PySpark not available locally (will work in Docker)")
    
    if delta_available:
        logger.info("✅ Delta Lake is available")
    else:
        logger.warning("⚠️  Delta Lake not available (can be installed with: pip install delta-spark)")
    
    return True

def test_configuration_loading():
    """
    Business Purpose: Test that configuration can be loaded without errors
    """
    logger.info("Testing configuration loading...")
    
    try:
        # Add utilities to path
        sys.path.append(os.path.join(os.path.dirname(__file__), 'utilities'))
        
        from config import config
        
        logger.info(f"✅ Configuration loaded successfully")
        logger.info(f"   Docker base path: {config.DOCKER_BASE_PATH}")
        logger.info(f"   Catalog name: {config.CATALOG_NAME}")
        logger.info(f"   Schema name: {config.SCHEMA_NAME}")
        
        # Test schema definitions
        customer_schema = config.get_customer_schema()
        transaction_schema = config.get_transactions_schema()
        
        logger.info(f"✅ Customer schema: {len(customer_schema.fields)} fields")
        logger.info(f"✅ Transaction schema: {len(transaction_schema.fields)} fields")
        
        # Test business rules
        logger.info(f"✅ Valid account types: {config.VALID_ACCOUNT_TYPES}")
        logger.info(f"✅ Valid channels: {config.VALID_CHANNELS}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration loading failed: {e}")
        return False

def test_data_quality_functions():
    """
    Business Purpose: Test data quality utility functions
    """
    logger.info("Testing data quality functions...")

    try:
        sys.path.append(os.path.join(os.path.dirname(__file__), 'utilities'))
        from data_quality import DataQualityUtils

        logger.info("✅ Data quality utilities imported successfully")

        # Test that the class and methods exist
        utils = DataQualityUtils()

        # Check if methods exist
        methods = ['validate_email', 'clean_phone_number', 'calculate_age', 'calculate_risk_score']
        for method in methods:
            if hasattr(DataQualityUtils, method):
                logger.info(f"✅ Method found: {method}")
            else:
                logger.warning(f"⚠️  Method not found: {method}")

        logger.info("✅ Data quality functions structure verified")
        return True

    except Exception as e:
        logger.error(f"❌ Data quality functions test failed: {e}")
        return False

def test_vs_code_configuration():
    """
    Business Purpose: Test VS Code configuration files
    """
    logger.info("Testing VS Code configuration...")
    
    vscode_files = [
        ".vscode/launch.json",
        ".vscode/tasks.json"
    ]
    
    for file_path in vscode_files:
        if os.path.exists(file_path):
            logger.info(f"✅ VS Code config found: {file_path}")
            
            # Test if it's valid JSON
            try:
                import json
                with open(file_path, 'r', encoding='utf-8') as f:
                    json.load(f)
                logger.info(f"✅ Valid JSON: {file_path}")
            except (json.JSONDecodeError, UnicodeDecodeError) as e:
                logger.error(f"❌ Invalid JSON in {file_path}: {e}")
                return False
        else:
            logger.warning(f"⚠️  VS Code config not found: {file_path}")
    
    return True

def generate_docker_instructions():
    """
    Business Purpose: Generate instructions for Docker deployment
    """
    logger.info("\n" + "="*60)
    logger.info("🐳 DOCKER DEPLOYMENT INSTRUCTIONS")
    logger.info("="*60)
    logger.info("Your banking pipeline is ready for Docker deployment!")
    logger.info("")
    logger.info("📋 Next Steps:")
    logger.info("1. Connect VS Code to your Docker container:")
    logger.info("   - Install 'Remote - Containers' extension")
    logger.info("   - Press Ctrl+Shift+P → 'Remote-Containers: Attach to Running Container'")
    logger.info("   - Select your PySpark container")
    logger.info("")
    logger.info("2. Copy project files to Docker container:")
    logger.info("   docker cp . <container_name>:/home/<USER>/banking_pipeline")
    logger.info("")
    logger.info("3. In the Docker container, run:")
    logger.info("   cd /home/<USER>/banking_pipeline")
    logger.info("   python install_delta_lake.py  # Optional but recommended")
    logger.info("   python test_docker_setup.py   # Verify setup")
    logger.info("   python docker_pipeline_runner.py  # Run pipeline")
    logger.info("")
    logger.info("📁 Your data structure should be:")
    logger.info("/home/<USER>/Volumes/dlt_banking_project_catalog/dlt_project_schema/raw/")
    logger.info("├── 2023/")
    logger.info("│   ├── customers/")
    logger.info("│   └── accounts/")
    logger.info("└── 2024/")
    logger.info("    ├── customers/")
    logger.info("    └── accounts/")
    logger.info("")
    logger.info("🎯 Business Value:")
    logger.info("✅ Complete Medallion Architecture implementation")
    logger.info("✅ Both DLT and PySpark approaches")
    logger.info("✅ Banking-specific business rules and validations")
    logger.info("✅ Customer analytics and risk management")
    logger.info("✅ Executive reporting and KPIs")
    logger.info("="*60)

def run_comprehensive_test():
    """
    Business Purpose: Run all Windows setup tests
    """
    logger.info("🪟 WINDOWS BANKING PIPELINE SETUP TEST")
    logger.info("=" * 50)
    logger.info("Business Purpose: Verify project is ready for Docker deployment")
    
    test_results = {
        "project_structure": False,
        "python_imports": False,
        "configuration": False,
        "data_quality": False,
        "vscode_config": False
    }
    
    # Run all tests
    test_results["project_structure"] = test_project_structure()
    test_results["python_imports"] = test_python_imports()
    test_results["configuration"] = test_configuration_loading()
    test_results["data_quality"] = test_data_quality_functions()
    test_results["vscode_config"] = test_vs_code_configuration()
    
    # Generate summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 TEST RESULTS SUMMARY")
    logger.info("=" * 50)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name.replace('_', ' ').title()}: {status}")
    
    logger.info(f"\nOverall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 ALL TESTS PASSED! Your project is ready for Docker deployment.")
        generate_docker_instructions()
    else:
        logger.error("⚠️  Some tests failed. Please fix the issues above.")
    
    return test_results

def main():
    """
    Business Purpose: Main test execution function
    """
    try:
        results = run_comprehensive_test()
        
        # Exit with appropriate code
        if all(results.values()):
            sys.exit(0)  # Success
        else:
            sys.exit(1)  # Failure
            
    except Exception as e:
        logger.error(f"Test execution failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
