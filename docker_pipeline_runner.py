"""
Docker PySpark Pipeline Runner for Banking Data
Business Purpose: Run the complete banking pipeline in Docker PySpark environment
Optimized for VS Code development with Docker containers
"""

import os
import sys
from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
import logging
from typing import Dict, List
from pathlib import Path

# Add project paths
sys.path.append(os.path.join(os.path.dirname(__file__), 'utilities'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'bronze'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'silver'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'gold'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'tests'))

from config import config
from bronze_layer_pyspark import BronzeLayerProcessor
from silver_layer_pyspark import Silver<PERSON>ayerProcessor
from gold_layer_pyspark import GoldLayerProcessor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DockerBankingPipeline:
    """
    Docker-optimized banking pipeline for PySpark
    Business Purpose: Execute banking data pipeline in containerized environment
    """
    
    def __init__(self):
        self.spark = self._initialize_spark()
        self.config = config
        self._setup_directories()
        
        # Initialize processors
        self.bronze_processor = BronzeLayerProcessor(self.spark)
        self.silver_processor = SilverLayerProcessor(self.spark)
        self.gold_processor = GoldLayerProcessor(self.spark)
        
        logger.info("Docker Banking Pipeline initialized successfully")
    
    def _initialize_spark(self) -> SparkSession:
        """
        Business Purpose: Initialize Spark session optimized for Docker environment
        """
        logger.info("Initializing Spark session for Docker environment...")
        
        return SparkSession.builder \
            .appName("Docker Banking Data Pipeline") \
            .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
            .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
            .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
            .config("spark.sql.adaptive.enabled", "true") \
            .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
            .config("spark.sql.adaptive.skewJoin.enabled", "true") \
            .getOrCreate()
    
    def _setup_directories(self):
        """
        Business Purpose: Create necessary directories for pipeline execution
        """
        directories = [
            self.config.CHECKPOINT_PATH,
            self.config.BRONZE_PATH,
            self.config.SILVER_PATH,
            self.config.GOLD_PATH
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            logger.info(f"Directory ensured: {directory}")
    
    def discover_data_files(self) -> Dict[str, List[str]]:
        """
        Business Purpose: Automatically discover available data files in your structure
        """
        logger.info("Discovering available data files...")
        
        data_files = {
            "customer_files": [],
            "transaction_files": []
        }
        
        # Check for customer files
        customer_paths = [
            self.config.RAW_CUSTOMER_2023_PATH,
            self.config.RAW_CUSTOMER_2024_PATH
        ]
        
        for path in customer_paths:
            if os.path.exists(path):
                files = [f for f in os.listdir(path) if f.endswith('.csv')]
                for file in files:
                    full_path = os.path.join(path, file)
                    data_files["customer_files"].append(full_path)
                    logger.info(f"Found customer file: {full_path}")
        
        # Check for transaction files
        transaction_paths = [
            self.config.RAW_ACCOUNTS_2023_PATH,
            self.config.RAW_ACCOUNTS_2024_PATH
        ]
        
        for path in transaction_paths:
            if os.path.exists(path):
                files = [f for f in os.listdir(path) if f.endswith('.csv')]
                for file in files:
                    full_path = os.path.join(path, file)
                    data_files["transaction_files"].append(full_path)
                    logger.info(f"Found transaction file: {full_path}")
        
        logger.info(f"Discovery complete: {len(data_files['customer_files'])} customer files, {len(data_files['transaction_files'])} transaction files")
        return data_files
    
    def load_and_combine_data(self, file_list: List[str], data_type: str) -> None:
        """
        Business Purpose: Load and combine multiple CSV files into unified datasets
        """
        logger.info(f"Loading and combining {data_type} data files...")
        
        if not file_list:
            logger.warning(f"No {data_type} files found to process")
            return None
        
        combined_df = None
        
        for file_path in file_list:
            logger.info(f"Processing file: {file_path}")
            
            if data_type == "customer":
                schema = self.config.get_customer_schema()
            else:
                schema = self.config.get_transactions_schema()
            
            try:
                df = self.spark.read \
                    .option("header", "true") \
                    .option("multiline", "true") \
                    .option("escape", '"') \
                    .schema(schema) \
                    .csv(file_path)
                
                # Add source file information for tracking
                df = df.withColumn("_source_file", lit(file_path)) \
                       .withColumn("_ingestion_timestamp", current_timestamp())
                
                if combined_df is None:
                    combined_df = df
                else:
                    combined_df = combined_df.union(df)
                
                logger.info(f"Successfully loaded {df.count()} records from {file_path}")
                
            except Exception as e:
                logger.error(f"Failed to load {file_path}: {str(e)}")
                continue
        
        if combined_df:
            # Save combined data for processing
            output_path = f"{self.config.RAW_VOLUME_PATH}/combined_{data_type}"
            combined_df.coalesce(1).write \
                .mode("overwrite") \
                .option("header", "true") \
                .csv(output_path)
            
            logger.info(f"Combined {data_type} data saved to: {output_path}")
            logger.info(f"Total {data_type} records: {combined_df.count()}")
            
            return output_path
        
        return None
    
    def run_bronze_layer(self, customer_path: str, transaction_path: str):
        """
        Business Purpose: Execute bronze layer processing with Docker paths
        """
        logger.info("=== BRONZE LAYER PROCESSING ===")
        logger.info("Business Goal: Clean and validate raw banking data for compliance")
        
        if customer_path:
            logger.info("Processing customer data to bronze layer...")
            bronze_customer_path = f"{self.config.BRONZE_PATH}/customer"
            self.bronze_processor.process_customer_data(customer_path, bronze_customer_path)
            logger.info(f"Bronze customer data saved to: {bronze_customer_path}")
        
        if transaction_path:
            logger.info("Processing transaction data to bronze layer...")
            bronze_transaction_path = f"{self.config.BRONZE_PATH}/transaction"
            self.bronze_processor.process_transaction_data(transaction_path, bronze_transaction_path)
            logger.info(f"Bronze transaction data saved to: {bronze_transaction_path}")
        
        return {
            "bronze_customer": bronze_customer_path if customer_path else None,
            "bronze_transaction": bronze_transaction_path if transaction_path else None
        }
    
    def run_silver_layer(self, bronze_paths: Dict[str, str]):
        """
        Business Purpose: Execute silver layer processing with enrichment
        """
        logger.info("=== SILVER LAYER PROCESSING ===")
        logger.info("Business Goal: Enrich data with analytics features and customer context")
        
        silver_paths = {}
        
        if bronze_paths.get("bronze_customer"):
            logger.info("Enriching customer data...")
            silver_customer_path = f"{self.config.SILVER_PATH}/customer"
            self.silver_processor.process_customer_enrichment(
                bronze_paths["bronze_customer"], 
                silver_customer_path
            )
            silver_paths["silver_customer"] = silver_customer_path
            logger.info(f"Silver customer data saved to: {silver_customer_path}")
        
        if bronze_paths.get("bronze_transaction") and bronze_paths.get("bronze_customer"):
            logger.info("Enriching transaction data with customer context...")
            silver_transaction_path = f"{self.config.SILVER_PATH}/transaction"
            self.silver_processor.process_transaction_enrichment(
                bronze_paths["bronze_transaction"],
                bronze_paths["bronze_customer"],
                silver_transaction_path
            )
            silver_paths["silver_transaction"] = silver_transaction_path
            logger.info(f"Silver transaction data saved to: {silver_transaction_path}")
            
            logger.info("Creating account summaries...")
            silver_account_path = f"{self.config.SILVER_PATH}/account"
            self.silver_processor.process_account_summary(
                silver_transaction_path,
                silver_account_path
            )
            silver_paths["silver_account"] = silver_account_path
            logger.info(f"Silver account data saved to: {silver_account_path}")
        
        return silver_paths
    
    def run_gold_layer(self, silver_paths: Dict[str, str]):
        """
        Business Purpose: Execute gold layer processing for business analytics
        """
        logger.info("=== GOLD LAYER PROCESSING ===")
        logger.info("Business Goal: Create business KPIs and executive analytics")
        
        gold_paths = {}
        
        if silver_paths.get("silver_customer") and silver_paths.get("silver_account"):
            logger.info("Creating customer analytics...")
            gold_customer_analytics_path = f"{self.config.GOLD_PATH}/customer_analytics"
            self.gold_processor.process_customer_analytics(
                silver_paths["silver_customer"],
                silver_paths["silver_account"],
                gold_customer_analytics_path
            )
            gold_paths["gold_customer_analytics"] = gold_customer_analytics_path
            logger.info(f"Gold customer analytics saved to: {gold_customer_analytics_path}")
        
        if silver_paths.get("silver_transaction"):
            logger.info("Creating transaction summaries...")
            gold_transaction_summary_path = f"{self.config.GOLD_PATH}/transaction_summary"
            self.gold_processor.process_transaction_summary(
                silver_paths["silver_transaction"],
                gold_transaction_summary_path
            )
            gold_paths["gold_transaction_summary"] = gold_transaction_summary_path
            logger.info(f"Gold transaction summary saved to: {gold_transaction_summary_path}")
        
        if silver_paths.get("silver_customer") and silver_paths.get("silver_transaction"):
            logger.info("Creating risk metrics...")
            gold_risk_metrics_path = f"{self.config.GOLD_PATH}/risk_metrics"
            self.gold_processor.process_risk_metrics(
                silver_paths["silver_customer"],
                silver_paths["silver_transaction"],
                gold_risk_metrics_path
            )
            gold_paths["gold_risk_metrics"] = gold_risk_metrics_path
            logger.info(f"Gold risk metrics saved to: {gold_risk_metrics_path}")
        
        if all(path in silver_paths for path in ["silver_customer", "silver_transaction", "silver_account"]):
            logger.info("Creating monthly KPIs...")
            gold_monthly_kpi_path = f"{self.config.GOLD_PATH}/monthly_kpi"
            self.gold_processor.process_monthly_kpi(
                silver_paths["silver_customer"],
                silver_paths["silver_transaction"],
                silver_paths["silver_account"],
                gold_monthly_kpi_path
            )
            gold_paths["gold_monthly_kpi"] = gold_monthly_kpi_path
            logger.info(f"Gold monthly KPI saved to: {gold_monthly_kpi_path}")
        
        return gold_paths
    
    def run_complete_pipeline(self):
        """
        Business Purpose: Execute the complete banking data pipeline
        """
        logger.info("🏦 STARTING COMPLETE BANKING DATA PIPELINE")
        logger.info("Business Purpose: Transform raw banking data into actionable business insights")
        
        try:
            # Step 1: Discover available data files
            data_files = self.discover_data_files()
            
            # Step 2: Load and combine data
            customer_path = self.load_and_combine_data(data_files["customer_files"], "customer")
            transaction_path = self.load_and_combine_data(data_files["transaction_files"], "transaction")
            
            if not customer_path and not transaction_path:
                logger.error("No data files found to process!")
                return
            
            # Step 3: Bronze layer processing
            bronze_paths = self.run_bronze_layer(customer_path, transaction_path)
            
            # Step 4: Silver layer processing
            silver_paths = self.run_silver_layer(bronze_paths)
            
            # Step 5: Gold layer processing
            gold_paths = self.run_gold_layer(silver_paths)
            
            # Step 6: Generate summary
            self.generate_pipeline_summary(bronze_paths, silver_paths, gold_paths)
            
            logger.info("✅ PIPELINE EXECUTION COMPLETED SUCCESSFULLY!")
            logger.info("📊 Business insights and analytics are now available")
            
        except Exception as e:
            logger.error(f"❌ Pipeline execution failed: {str(e)}")
            raise
        finally:
            self.spark.stop()
    
    def generate_pipeline_summary(self, bronze_paths, silver_paths, gold_paths):
        """
        Business Purpose: Generate executive summary of pipeline results
        """
        logger.info("\n" + "="*60)
        logger.info("📊 BANKING PIPELINE EXECUTION SUMMARY")
        logger.info("="*60)
        
        # Count records in each layer
        for layer_name, paths in [("Bronze", bronze_paths), ("Silver", silver_paths), ("Gold", gold_paths)]:
            logger.info(f"\n{layer_name} Layer Results:")
            for table_name, path in paths.items():
                if path and os.path.exists(path):
                    try:
                        df = self.spark.read.format("delta").load(path)
                        count = df.count()
                        logger.info(f"  ✅ {table_name}: {count:,} records")
                    except:
                        logger.info(f"  ⚠️  {table_name}: Path exists but couldn't read")
                else:
                    logger.info(f"  ❌ {table_name}: Not created")
        
        logger.info("\n🎯 BUSINESS IMPACT:")
        logger.info("  • Customer data cleaned and validated for KYC compliance")
        logger.info("  • Transaction data processed for fraud detection")
        logger.info("  • Customer analytics generated for strategic insights")
        logger.info("  • Risk metrics calculated for compliance monitoring")
        logger.info("  • Executive KPIs created for business reporting")
        
        logger.info(f"\n📁 Data Location: {self.config.DOCKER_BASE_PATH}")
        logger.info("="*60)

def main():
    """
    Business Purpose: Main execution function for Docker environment
    """
    print("🐳 DOCKER BANKING DATA PIPELINE")
    print("================================")
    print("Business Goal: Execute banking pipeline in containerized environment")
    print("Optimized for VS Code development with Docker PySpark")
    
    try:
        pipeline = DockerBankingPipeline()
        pipeline.run_complete_pipeline()
        
    except Exception as e:
        logger.error(f"Pipeline execution failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
