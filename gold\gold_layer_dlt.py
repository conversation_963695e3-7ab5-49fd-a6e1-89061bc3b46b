"""
Gold Layer - Delta Live Tables Implementation
Business Purpose: Create business-specific aggregations, KPIs, and analytics marts
This layer provides curated datasets for executive reporting, ML features, and business intelligence
"""

import dlt
from pyspark.sql.functions import *
from pyspark.sql.types import *
from pyspark.sql.window import Window
import sys
import os

# Add utilities to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'utilities'))
from config import config
from data_quality import DataQualityUtils, BusinessRules

# ============================================================================
# GOLD CUSTOMER ANALYTICS - EXECUTIVE DASHBOARD METRICS
# ============================================================================

@dlt.table(
    name=config.GOLD_CUSTOMER_ANALYTICS_TABLE,
    comment="""
    Gold layer customer analytics for executive reporting and strategic planning.
    Business Purpose: Provide customer insights for business strategy, product development,
    and market segmentation decisions.
    """,
    table_properties={
        "quality": "gold",
        "pipelines.autoOptimize.managed": "true"
    }
)
def gold_customer_analytics():
    """
    Business Context: Create customer analytics for strategic business decisions
    Why: Enable data-driven customer strategy and product development
    What: Customer segmentation, lifetime value, and behavioral insights
    """
    
    customer_df = dlt.read(config.SILVER_CUSTOMER_TABLE)
    account_df = dlt.read(config.SILVER_ACCOUNTS_TABLE)
    
    # Join customer with account summaries
    analytics_df = customer_df.alias("c").join(
        account_df.alias("a"),
        col("c.customer_id") == col("a.customer_id"),
        "left"
    )
    
    # Business Metric: Customer Lifetime Value Estimation
    analytics_df = analytics_df.withColumn("estimated_clv",
        col("net_transaction_amount") * 
        (col("days_since_joining") / 365.0) * 
        col("digital_adoption_score") * 
        when(col("customer_value_segment") == "HIGH_VALUE", 2.0)
        .when(col("customer_value_segment") == "MEDIUM_VALUE", 1.5)
        .otherwise(1.0)
    )
    
    # Business Metric: Customer Engagement Score
    analytics_df = analytics_df.withColumn("engagement_score",
        (col("total_transactions") / greatest(col("days_since_joining"), lit(1))) * 365 +
        col("digital_adoption_score") * 10 +
        col("channels_used") * 5 +
        when(col("customer_status") == "ACTIVE", 20).otherwise(0)
    )
    
    # Business Metric: Churn Risk Score
    analytics_df = analytics_df.withColumn("churn_risk_score",
        when(col("days_since_joining") < 90, 3)  # New customers at risk
        .when(col("total_transactions") < 5, 4)  # Low activity
        .when(col("current_balance") < 0, 5)     # Negative balance
        .when(col("customer_status") != "ACTIVE", 5)  # Inactive status
        .otherwise(1)
    )
    
    # Business Metric: Product Affinity Scoring
    analytics_df = analytics_df.withColumn("savings_affinity",
        when(col("account_type") == "SAVINGS", 5)
        .when(col("income_segment_numeric") >= 2, 3)
        .when(col("age_segment").isin(["GEN_X", "BABY_BOOMER"]), 4)
        .otherwise(2)
    )
    
    analytics_df = analytics_df.withColumn("credit_affinity",
        when(col("account_type") == "CREDIT", 5)
        .when((col("income_segment_numeric") >= 2) & (col("calculated_risk_score") <= 2), 4)
        .when(col("age_segment").isin(["MILLENNIAL", "GEN_X"]), 3)
        .otherwise(1)
    )
    
    # Business Metric: Digital Transformation Readiness
    analytics_df = analytics_df.withColumn("digital_readiness_score",
        col("digital_adoption_score") * 2 +
        when(col("age_segment").isin(["YOUNG_ADULT", "MILLENNIAL"]), 3).otherwise(1) +
        when(col("channels_used") > 2, 2).otherwise(0)
    )
    
    # Select final analytics columns
    final_df = analytics_df.select(
        col("c.customer_id"),
        col("c.name"),
        col("c.age_segment"),
        col("c.customer_tenure_category"),
        col("c.income_range"),
        col("c.digital_adoption_score"),
        col("a.customer_value_segment"),
        col("a.total_transactions"),
        col("a.current_balance"),
        col("estimated_clv"),
        col("engagement_score"),
        col("churn_risk_score"),
        col("savings_affinity"),
        col("credit_affinity"),
        col("digital_readiness_score"),
        current_timestamp().alias("analytics_generated_timestamp"),
        current_date().alias("analytics_generated_date")
    )
    
    return final_df

# ============================================================================
# GOLD TRANSACTION SUMMARY - BUSINESS PERFORMANCE METRICS
# ============================================================================

@dlt.table(
    name=config.GOLD_TRANSACTION_SUMMARY_TABLE,
    comment="""
    Gold layer transaction summary for business performance monitoring.
    Business Purpose: Provide transaction insights for revenue tracking,
    operational efficiency, and fraud monitoring.
    """,
    table_properties={
        "quality": "gold",
        "pipelines.autoOptimize.managed": "true"
    }
)
def gold_transaction_summary():
    """
    Business Context: Create transaction summaries for business performance monitoring
    Why: Enable operational excellence and revenue optimization
    What: Transaction volumes, revenue metrics, and operational KPIs
    """
    
    transactions_df = dlt.read(config.SILVER_TRANSACTIONS_TABLE)
    
    # Daily transaction summaries
    daily_summary = transactions_df.groupBy(
        col("txn_date"),
        col("txn_type"),
        col("txn_channel"),
        col("account_type")
    ).agg(
        count("txn_id").alias("transaction_count"),
        sum("standardized_txn_amount").alias("total_amount"),
        avg("standardized_txn_amount").alias("avg_amount"),
        countDistinct("customer_id").alias("unique_customers"),
        countDistinct("account_id").alias("unique_accounts"),
        sum(when(col("is_suspicious_amount") == True, 1).otherwise(0)).alias("suspicious_count"),
        sum(when(col("transaction_risk_score") > 2, 1).otherwise(0)).alias("high_risk_count")
    )
    
    # Business Metric: Channel Performance Score
    daily_summary = daily_summary.withColumn("channel_performance_score",
        (col("transaction_count") / 100.0) +
        (col("total_amount") / 10000.0) +
        when(col("suspicious_count") == 0, 10).otherwise(0) -
        (col("high_risk_count") * 2)
    )
    
    # Business Metric: Operational Efficiency Score
    daily_summary = daily_summary.withColumn("operational_efficiency_score",
        col("transaction_count") / greatest(col("unique_customers"), lit(1)) +
        when(col("txn_channel").isin(["MOBILE", "ONLINE"]), 2).otherwise(1)
    )
    
    # Add metadata
    final_summary = daily_summary.withColumn("summary_generated_timestamp", current_timestamp()) \
                                .withColumn("summary_generated_date", current_date())
    
    return final_summary

# ============================================================================
# GOLD RISK METRICS - COMPLIANCE AND RISK MANAGEMENT
# ============================================================================

@dlt.table(
    name=config.GOLD_RISK_METRICS_TABLE,
    comment="""
    Gold layer risk metrics for compliance monitoring and risk management.
    Business Purpose: Provide risk insights for regulatory compliance,
    fraud prevention, and portfolio risk management.
    """,
    table_properties={
        "quality": "gold",
        "pipelines.autoOptimize.managed": "true"
    }
)
def gold_risk_metrics():
    """
    Business Context: Create risk metrics for compliance and risk management
    Why: Ensure regulatory compliance and minimize operational risk
    What: Risk scores, compliance metrics, and fraud indicators
    """
    
    customer_df = dlt.read(config.SILVER_CUSTOMER_TABLE)
    transactions_df = dlt.read(config.SILVER_TRANSACTIONS_TABLE)
    
    # Customer risk aggregations
    customer_risk = customer_df.groupBy(
        col("risk_segment"),
        col("age_segment"),
        col("income_range"),
        col("customer_tenure_category")
    ).agg(
        count("customer_id").alias("customer_count"),
        avg("calculated_risk_score").alias("avg_risk_score"),
        sum(when(col("standardized_status") == "ACTIVE", 1).otherwise(0)).alias("active_customers"),
        avg("digital_adoption_score").alias("avg_digital_adoption")
    )
    
    # Transaction risk aggregations
    transaction_risk = transactions_df.groupBy(
        date_trunc("month", col("txn_date")).alias("risk_month"),
        col("txn_channel"),
        col("customer_city")
    ).agg(
        count("txn_id").alias("total_transactions"),
        sum(when(col("is_suspicious_amount") == True, 1).otherwise(0)).alias("suspicious_transactions"),
        sum(when(col("transaction_risk_score") > 2, 1).otherwise(0)).alias("high_risk_transactions"),
        avg("transaction_risk_score").alias("avg_transaction_risk"),
        sum("standardized_txn_amount").alias("total_transaction_value")
    )
    
    # Business Metric: Risk Concentration Score
    transaction_risk = transaction_risk.withColumn("risk_concentration_score",
        (col("suspicious_transactions") / greatest(col("total_transactions"), lit(1))) * 100 +
        (col("high_risk_transactions") / greatest(col("total_transactions"), lit(1))) * 50
    )
    
    # Business Metric: Compliance Score
    transaction_risk = transaction_risk.withColumn("compliance_score",
        when(col("risk_concentration_score") <= 5, 5)
        .when(col("risk_concentration_score") <= 10, 4)
        .when(col("risk_concentration_score") <= 20, 3)
        .when(col("risk_concentration_score") <= 30, 2)
        .otherwise(1)
    )
    
    # Combine risk metrics
    combined_risk = customer_risk.crossJoin(transaction_risk) \
                                .withColumn("risk_assessment_timestamp", current_timestamp()) \
                                .withColumn("risk_assessment_date", current_date())
    
    return combined_risk

# ============================================================================
# GOLD MONTHLY KPI - EXECUTIVE DASHBOARD
# ============================================================================

@dlt.table(
    name=config.GOLD_MONTHLY_KPI_TABLE,
    comment="""
    Gold layer monthly KPIs for executive dashboard and strategic planning.
    Business Purpose: Provide high-level business metrics for executive reporting
    and strategic decision making.
    """,
    table_properties={
        "quality": "gold",
        "pipelines.autoOptimize.managed": "true"
    }
)
def gold_monthly_kpi():
    """
    Business Context: Create monthly KPIs for executive reporting
    Why: Enable strategic decision making with high-level business metrics
    What: Revenue, growth, customer acquisition, and operational KPIs
    """
    
    customer_df = dlt.read(config.SILVER_CUSTOMER_TABLE)
    transactions_df = dlt.read(config.SILVER_TRANSACTIONS_TABLE)
    accounts_df = dlt.read(config.SILVER_ACCOUNTS_TABLE)
    
    # Monthly customer metrics
    monthly_customers = customer_df.withColumn("join_month", 
        date_trunc("month", to_date(col("join_date")))) \
        .groupBy("join_month") \
        .agg(
            count("customer_id").alias("new_customers"),
            countDistinct("city").alias("cities_served"),
            avg("customer_age").alias("avg_customer_age"),
            sum(when(col("digital_adoption_score") >= 3, 1).otherwise(0)).alias("digital_customers")
        )
    
    # Monthly transaction metrics
    monthly_transactions = transactions_df.withColumn("txn_month",
        date_trunc("month", col("txn_date"))) \
        .groupBy("txn_month") \
        .agg(
            count("txn_id").alias("total_transactions"),
            sum("standardized_txn_amount").alias("total_revenue"),
            countDistinct("customer_id").alias("active_customers"),
            avg("standardized_txn_amount").alias("avg_transaction_value"),
            sum(when(col("txn_channel").isin(["MOBILE", "ONLINE"]), 1).otherwise(0)).alias("digital_transactions")
        )
    
    # Monthly account metrics
    monthly_accounts = accounts_df.groupBy(
        date_trunc("month", col("silver_processed_date")).alias("account_month")
    ).agg(
        count("account_id").alias("total_accounts"),
        sum("current_balance").alias("total_deposits"),
        sum(when(col("customer_value_segment") == "HIGH_VALUE", 1).otherwise(0)).alias("high_value_accounts"),
        avg("account_health_score").alias("avg_account_health")
    )
    
    # Join all monthly metrics
    monthly_kpi = monthly_customers.alias("c") \
        .join(monthly_transactions.alias("t"), 
              col("c.join_month") == col("t.txn_month"), "full_outer") \
        .join(monthly_accounts.alias("a"),
              coalesce(col("c.join_month"), col("t.txn_month")) == col("a.account_month"), "full_outer") \
        .select(
            coalesce(col("c.join_month"), col("t.txn_month"), col("a.account_month")).alias("kpi_month"),
            coalesce(col("new_customers"), lit(0)).alias("new_customers"),
            coalesce(col("total_transactions"), lit(0)).alias("total_transactions"),
            coalesce(col("total_revenue"), lit(0)).alias("total_revenue"),
            coalesce(col("active_customers"), lit(0)).alias("active_customers"),
            coalesce(col("total_accounts"), lit(0)).alias("total_accounts"),
            coalesce(col("total_deposits"), lit(0)).alias("total_deposits"),
            coalesce(col("high_value_accounts"), lit(0)).alias("high_value_accounts"),
            coalesce(col("digital_customers"), lit(0)).alias("digital_customers"),
            coalesce(col("digital_transactions"), lit(0)).alias("digital_transactions"),
            coalesce(col("avg_customer_age"), lit(0)).alias("avg_customer_age"),
            coalesce(col("avg_transaction_value"), lit(0)).alias("avg_transaction_value"),
            coalesce(col("avg_account_health"), lit(0)).alias("avg_account_health")
        )
    
    # Business KPIs calculation
    monthly_kpi = monthly_kpi.withColumn("digital_adoption_rate",
        col("digital_transactions") / greatest(col("total_transactions"), lit(1)) * 100)
    
    monthly_kpi = monthly_kpi.withColumn("customer_acquisition_cost",
        col("total_revenue") / greatest(col("new_customers"), lit(1)))
    
    monthly_kpi = monthly_kpi.withColumn("revenue_per_customer",
        col("total_revenue") / greatest(col("active_customers"), lit(1)))
    
    monthly_kpi = monthly_kpi.withColumn("account_growth_rate",
        (col("total_accounts") - lag(col("total_accounts"), 1).over(
            Window.orderBy("kpi_month"))) / 
        greatest(lag(col("total_accounts"), 1).over(Window.orderBy("kpi_month")), lit(1)) * 100)
    
    # Add metadata
    final_kpi = monthly_kpi.withColumn("kpi_generated_timestamp", current_timestamp()) \
                          .withColumn("kpi_generated_date", current_date())
    
    return final_kpi
