"""
Delta Lake Test Script for Docker Environment
Business Purpose: Verify Delta Lake is working properly with your PySpark setup
"""

import os
import sys
from pyspark.sql import SparkSession
from pyspark.sql.functions import *
import logging
import shutil

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_delta_lake_installation():
    """Test Delta Lake package installation"""
    logger.info("Testing Delta Lake installation...")
    
    try:
        import delta
        logger.info(f"✅ Delta Lake imported successfully - Version: {delta.__version__}")
        return True
    except ImportError as e:
        logger.error(f"❌ Delta Lake import failed: {e}")
        return False

def test_spark_with_delta():
    """Test Spark session with Delta Lake extensions"""
    logger.info("Testing Spark with Delta Lake extensions...")
    
    try:
        spark = SparkSession.builder \
            .appName("Delta Lake Test") \
            .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
            .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
            .getOrCreate()
        
        logger.info(f"✅ Spark with Delta initialized - Version: {spark.version}")
        return spark
        
    except Exception as e:
        logger.error(f"❌ Spark with Delta initialization failed: {e}")
        return None

def test_delta_operations(spark):
    """Test basic Delta Lake operations"""
    logger.info("Testing Delta Lake operations...")
    
    test_path = "/tmp/delta_test_banking"
    
    try:
        # Clean up any existing test data
        if os.path.exists(test_path):
            shutil.rmtree(test_path)
        
        # Create test banking data
        test_data = [
            (1, "John Doe", "<EMAIL>", "2023-01-01", 1000.0),
            (2, "Jane Smith", "<EMAIL>", "2023-01-02", 1500.0),
            (3, "Bob Johnson", "<EMAIL>", "2023-01-03", 2000.0)
        ]
        
        columns = ["customer_id", "name", "email", "join_date", "balance"]
        
        df = spark.createDataFrame(test_data, columns)
        
        logger.info("Created test banking data:")
        df.show()
        
        # Write as Delta table
        logger.info("Writing as Delta table...")
        df.write.format("delta").mode("overwrite").save(test_path)
        logger.info(f"✅ Delta table written to: {test_path}")
        
        # Read back from Delta table
        logger.info("Reading from Delta table...")
        read_df = spark.read.format("delta").load(test_path)
        count = read_df.count()
        logger.info(f"✅ Read {count} records from Delta table")
        
        # Test Delta Lake features
        logger.info("Testing Delta Lake features...")
        
        # 1. Append operation
        new_data = [(4, "Alice Brown", "<EMAIL>", "2023-01-04", 2500.0)]
        new_df = spark.createDataFrame(new_data, columns)
        new_df.write.format("delta").mode("append").save(test_path)
        
        updated_df = spark.read.format("delta").load(test_path)
        new_count = updated_df.count()
        logger.info(f"✅ After append: {new_count} records")
        
        # 2. Update operation (using Delta Lake SQL)
        updated_df.createOrReplaceTempView("banking_customers")
        spark.sql(f"""
            UPDATE delta.`{test_path}` 
            SET balance = balance * 1.1 
            WHERE customer_id = 1
        """)
        
        final_df = spark.read.format("delta").load(test_path)
        logger.info("✅ Update operation completed")
        
        # Show final data
        logger.info("Final Delta table data:")
        final_df.show()
        
        # 3. Version history (if supported)
        try:
            history_df = spark.sql(f"DESCRIBE HISTORY delta.`{test_path}`")
            logger.info("✅ Delta Lake version history:")
            history_df.select("version", "operation", "operationMetrics").show(truncate=False)
        except Exception as e:
            logger.warning(f"⚠️  Version history not available: {e}")
        
        # Clean up
        if os.path.exists(test_path):
            shutil.rmtree(test_path)
            logger.info("✅ Test data cleaned up")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Delta Lake operations test failed: {e}")
        # Clean up on failure
        if os.path.exists(test_path):
            shutil.rmtree(test_path)
        return False

def test_banking_data_compatibility():
    """Test Delta Lake with your actual banking data structure"""
    logger.info("Testing Delta Lake with banking data structure...")
    
    try:
        spark = SparkSession.builder \
            .appName("Banking Data Delta Test") \
            .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
            .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
            .getOrCreate()
        
        # Create sample banking schema
        from pyspark.sql.types import *
        
        customer_schema = StructType([
            StructField('customer_id', LongType(), False),
            StructField('name', StringType(), True),
            StructField('email', StringType(), True),
            StructField('city', StringType(), True),
            StructField('join_date', StringType(), True),
            StructField('balance', DoubleType(), True)
        ])
        
        # Create sample data
        sample_data = [
            (1001, "Banking Customer 1", "<EMAIL>", "New York", "2023-01-01", 5000.0),
            (1002, "Banking Customer 2", "<EMAIL>", "London", "2023-01-02", 7500.0),
            (1003, "Banking Customer 3", "<EMAIL>", "Tokyo", "2023-01-03", 10000.0)
        ]
        
        df = spark.createDataFrame(sample_data, customer_schema)
        
        test_path = "/tmp/banking_delta_test"
        
        # Clean up
        if os.path.exists(test_path):
            shutil.rmtree(test_path)
        
        # Write as Delta
        df.write.format("delta").mode("overwrite").save(test_path)
        
        # Read and verify
        read_df = spark.read.format("delta").load(test_path)
        
        logger.info("✅ Banking data structure compatible with Delta Lake")
        logger.info("Sample banking data in Delta format:")
        read_df.show()
        
        # Clean up
        if os.path.exists(test_path):
            shutil.rmtree(test_path)
        
        spark.stop()
        return True
        
    except Exception as e:
        logger.error(f"❌ Banking data compatibility test failed: {e}")
        return False

def main():
    """Run comprehensive Delta Lake tests"""
    logger.info("🔺 DELTA LAKE COMPREHENSIVE TEST")
    logger.info("=" * 50)
    logger.info("Business Purpose: Verify Delta Lake is ready for banking pipeline")
    
    tests = [
        ("Delta Lake Installation", test_delta_lake_installation),
        ("Banking Data Compatibility", test_banking_data_compatibility)
    ]
    
    results = {}
    spark = None
    
    # Test installation first
    for test_name, test_func in tests[:1]:
        logger.info(f"\n--- {test_name} ---")
        results[test_name] = test_func()
    
    # Test Spark with Delta
    logger.info(f"\n--- Spark with Delta Lake ---")
    spark = test_spark_with_delta()
    results["Spark with Delta"] = spark is not None
    
    # Test Delta operations if Spark is working
    if spark:
        logger.info(f"\n--- Delta Lake Operations ---")
        results["Delta Operations"] = test_delta_operations(spark)
        spark.stop()
    
    # Test remaining
    for test_name, test_func in tests[1:]:
        logger.info(f"\n--- {test_name} ---")
        results[test_name] = test_func()
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 DELTA LAKE TEST RESULTS")
    logger.info("=" * 50)
    
    passed = len([r for r in results.values() if r])
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 DELTA LAKE IS FULLY FUNCTIONAL!")
        logger.info("✅ Your banking pipeline can use all Delta Lake features")
        logger.info("🚀 Ready to run: python docker_pipeline_runner.py")
    else:
        logger.error("⚠️  Some Delta Lake tests failed")
        logger.info("💡 You can still run the pipeline - it will use Parquet format")

if __name__ == "__main__":
    main()
